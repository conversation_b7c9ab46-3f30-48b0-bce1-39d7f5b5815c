{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^2.27.0", "@azure/msal-react": "^1.4.3", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@exim/core": "^1.1.3", "@mdi/js": "^7.0.96", "@mdi/react": "^1.6.1", "@mui/icons-material": "^5.8.4", "@mui/material": "^5.10.3", "@mui/styled-engine": "^5.10.0", "@react-pdf-viewer/core": "^3.7.0", "@react-pdf-viewer/default-layout": "^3.7.0", "@reduxjs/toolkit": "^1.9.3", "antd": "^5.22.5", "axios": "^1.6.5", "bignumber.js": "^9.1.2", "bootstrap": "^5.3.6", "bytes": "^3.1.2", "camelcase-keys": "^8.0.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "debug": "^4.3.4", "file-saver": "^2.0.5", "file-size": "^1.0.0", "i18next": "^21.8.10", "i18next-browser-languagedetector": "^6.1.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "query-string": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.1.0", "react-i18next": "^11.17.3", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.14.0", "react-lines-ellipsis": "^0.15.3", "react-loading": "^2.0.3", "react-number-format": "^5.3.1", "react-password-checklist": "^1.4.1", "react-redux": "^7.2.6", "react-router-dom": "^5.3.4", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.1", "snakecase-keys": "^5.4.6", "styled-components": "^6.1.8", "stylis": "^4.3.1", "universal-cookie": "^4.0.4", "url-join": "^5.0.0", "web-vitals": "^2.1.4"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "overrides": {"nth-check": "2.0.1", "debug": "$debug", "resolve-url-loader": {"postcss": "^8.4.33"}, "eslint-plugin-jsx-a11y": {"axe-core": "4.8.3-canary.3ce3e3d"}}, "scripts": {"start": "vite", "build": "GENERATE_SOURCEMAP=false vite build", "build:ci": "VITE_API_USER_URL=http://gtp-web/api/ VITE_NODE_URL=http://gtp-web VITE_API_URL=https://gtp-web VITE_API_VERSION=v1 vite build", "test": "jest --config jest.config.js --watchAll --no-coverage", "test:ci": "CI=true jest --coverage --config jest.config.js", "test:report": "CI=true jest --coverage --config jest.config.js", "test:all": "npm run lint && npm run test:ci", "lint": "eslint --ignore-path .gitignore --ext .ts  --ext .tsx src/ --max-warnings 0", "lint:fix": "npm run lint -- --fix", "preview": "vite preview --port 8082"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "6.6.3", "@testing-library/react": "14.3.1", "@testing-library/user-event": "14.6.1", "@trivago/prettier-plugin-sort-imports": "4.3.0", "@types/bytes": "^3.1.4", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/file-size": "^1.0.3", "@types/jest": "^29.5.14", "@types/lodash": "^4.14.202", "@types/node": "^20.10.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-helmet": "^6.1.11", "@types/react-lines-ellipsis": "^0.15.1", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@vitejs/plugin-react": "4.4.1", "eslint": "8.57.1", "eslint-config-google": "0.14.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "4.6.2", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-junit": "16.0.0", "prettier": "2.8.8", "react-inject-env": "2.1.0", "ts-jest": "29.3.4", "typescript": "4.7.4", "vite": "6.3.5", "vite-plugin-checker": "0.8.0"}, "jest-junit": {"outputDirectory": "report/unittest", "outputName": "junit.xml"}}