import { useSelector } from 'react-redux';

import { Col, Row } from 'antd';

import CustomTablePagination from 'common/CustomTablePagination';

import { useDelegatePermission } from 'hooks/setting/permission/useDelegatePermission';
import { settingDelegatePermissionSelector } from 'store/slice/setting/permission/delegatePermission';

import { Container } from './BodyResult.styles';
import useColumnResult from './ColumnResult';

export const BodyResultComponent = () => {
    const { filter, total, dataTable } = useSelector(settingDelegatePermissionSelector);
    const { limit, fetchDelegatePermission } = useDelegatePermission();

    const { columns } = useColumnResult();


    return (
        <Container>
            <Row>
                <Col span={24}>
                    <CustomTablePagination
                        limit={limit}
                        dataSource={dataTable}
                        columns={columns}
                        loading={false}
                        showLoading={true}
                        params={filter}
                        total={total}
                        onFetchData={fetchDelegatePermission}
                        tableProps={{
                            scroll: { x: 1500 },
                        }}
                    />
                </Col>
            </Row>
        </Container>
    );
};
