import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { EditOutlined } from '@mui/icons-material';
import { Row, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';

import colorConstant from 'constants/color.constant';
import type { ITFTableDelegatePermission } from 'types/Setting/Permission/DelegatePermission.type';

import { dateFormatter } from 'helper/ConvertDate';

const useColumnResult = () => {
    const { t } = useTranslation();

    const columns: ColumnsType<ITFTableDelegatePermission> = [
        {
            title: t('No.'),
            dataIndex: 'no',
            width: 100,
            key: 'no',
            align: 'center',
        },
        {
            title: t('Flow Name'),
            dataIndex: 'flowName',
            ellipsis: true,
            onHeaderCell: () => ({
                id: 'flowName',
                style: { textAlign: 'center' },
            }),
            onCell: (record: ITFTableDelegatePermission, rowIndex: any) => ({
                id: `flowName_${rowIndex}`,
                style: { textAlign: 'left' },
            }),
            sorter: true,
        },
        {
            title: t('Activity Name'),
            dataIndex: 'activityName',
            ellipsis: true,
            onHeaderCell: () => ({
                id: 'activityName',
                style: { textAlign: 'center' },
            }),
            onCell: (record: ITFTableDelegatePermission, rowIndex: any) => ({
                id: `activityName_${rowIndex}`,
                style: { textAlign: 'left' },
            }),
            sorter: true,
        },
        {
            title: t('Substitute Name'),
            dataIndex: 'substituteName',
            ellipsis: true,
            onHeaderCell: () => ({
                id: 'substituteName',
                style: { textAlign: 'center' },
            }),
            onCell: (record: ITFTableDelegatePermission, rowIndex: any) => ({
                id: `substituteName_${rowIndex}`,
                style: { textAlign: 'left' },
            }),
            sorter: true,
        },
        {
            title: t('Approver Name'),
            dataIndex: 'approvers',
            ellipsis: true,
            onHeaderCell: () => ({
                id: 'approvers',
                style: { textAlign: 'center' },
            }),
            onCell: (record: ITFTableDelegatePermission, rowIndex: any) => ({
                id: `approvers_${rowIndex}`,
                style: { textAlign: 'left' },
            }),
            render: (text: string[], record: ITFTableDelegatePermission, count: any) => {
                return (
                    <Row>
                        <div style={{ marginRight: 8 }}>{text[0] ?? ''}</div>
                        <Tooltip title={text?.filter((_, index) => index != 0).join(', ')}>
                            {text.length > 1 && (
                                <div style={{ color: colorConstant.TextLinkColor, cursor: 'pointer' }}>{`+${text?.length - 1}`}</div>
                            )}
                        </Tooltip>
                    </Row>
                );
            },
            sorter: true,
        },
        {
            title: t('Start Date'),
            dataIndex: 'startDate',
            width: 160,
            ellipsis: true,
            onHeaderCell: () => ({
                id: 'startDate',
            }),
            onCell: (record: ITFTableDelegatePermission, rowIndex: any) => ({
                id: `startDate_${rowIndex}`,
                style: { textAlign: 'center' },
            }),
            sorter: true,
            render: (text: any, record: ITFTableDelegatePermission, count: any) => {
                return dateFormatter(record.startDate, 'DD MMM YYYY HH:mm');
            },
        },
        {
            title: t('End Date '),
            dataIndex: 'endDate',
            width: 160,
            ellipsis: true,
            onHeaderCell: () => ({
                id: 'endDate',
            }),
            onCell: (record: ITFTableDelegatePermission, rowIndex: any) => ({
                id: `endDate_${rowIndex}`,
                style: { textAlign: 'center' },
            }),
            sorter: true,
            render: (text: any, record: ITFTableDelegatePermission, count: any) => {
                return dateFormatter(record.endDate, 'DD MMM YYYY HH:mm');
            },
        },
        {
            title: t('Edit'),
            dataIndex: 'edit',
            width: 150,
            ellipsis: true,
            onHeaderCell: () => ({
                id: 'edit',
            }),
            fixed: 'right',
            onCell: (record: ITFTableDelegatePermission, rowIndex: any) => ({
                id: `edit_${rowIndex}`,
                style: { textAlign: 'center' },
            }),
            render: (text: any, record: ITFTableDelegatePermission, count: any) => {
                return (
                    <>
                        <Link to={`/setting/permission/delegate-permission/edit/${record.substituteGroupId}`}>
                            <EditOutlined
                                id={`delegatePermissionEdit_${count}`}
                                style={{
                                    color: colorConstant.SecondaryColor,
                                    fontSize: '24px',
                                    cursor: 'pointer',
                                }}
                            />
                        </Link>
                    </>
                );
            },
        },
    ];

    return {
        columns,
    };
};

export default useColumnResult;
