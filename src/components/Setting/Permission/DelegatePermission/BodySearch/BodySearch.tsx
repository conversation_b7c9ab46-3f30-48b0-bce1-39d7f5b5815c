import { useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';

import { SearchOutlined } from '@ant-design/icons';
import { KeyboardArrowDown } from '@mui/icons-material';
import { Row } from 'antd';

import CustomCollapse from 'common/CustomCollapse/CustomCollapse';
import { TitleSemiBold22 } from 'common/Text.styles';

import { expandCollapse, hideCollapse } from 'store/slice/collapse';

import { Container } from './BodySearch.styles';
import type { ITFFormSearchProps } from './FormSearch/FormSearch';
import { FormSearch } from './FormSearch/FormSearch';

import isEmpty from 'lodash/isEmpty';

export const collapseKey = 'SETTING_MASTER_CUSTOMER_SERACH';

interface ITFBodySearchProps extends ITFFormSearchProps {}

export const BodySearchComponent = (props: ITFBodySearchProps) => {
    const { onReset = () => {} } = props;

    const dispatch = useDispatch();
    const expandIcon = useCallback(() => <KeyboardArrowDown id="buttonExpand" style={{ fontWeight: 700, fontSize: 32 }} />, []);

    useEffect(() => {
        return () => {
            onReset();
        };
    }, []);

    return (
        <Container>
            <CustomCollapse
                key={collapseKey}
                defaultActiveKey={['1']}
                expandIconPosition="end"
                expandIcon={expandIcon}
                collapseKey={collapseKey}
                onChange={(activeKey: string | string[]) => {
                    if (!isEmpty(activeKey)) {
                        dispatch(expandCollapse(collapseKey));
                    } else {
                        dispatch(hideCollapse(collapseKey));
                    }
                }}
                items={[
                    {
                        key: '1',
                        label: (
                            <Row className="header-collapse">
                                <SearchOutlined className="icon-collapse" />
                                <TitleSemiBold22 className="title-header">Search</TitleSemiBold22>
                            </Row>
                        ),
                        children: <FormSearch onReset={onReset} />,
                    },
                ]}
            />
        </Container>
    );
};
