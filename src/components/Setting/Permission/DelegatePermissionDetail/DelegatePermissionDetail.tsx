import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Link, useHistory, useParams } from 'react-router-dom';

import { CheckCircleOutlined, LeftOutlined } from '@ant-design/icons';
import { HowToRegOutlined } from '@mui/icons-material';
import type { SelectProps } from 'antd';
import { Card, Checkbox, Col, DatePicker, Divider, Form, Input, Row, Select, TimePicker } from 'antd';

import { ButtonPrimary, ButtonPrimaryOutline } from 'common/Button';
import CustomBreadcrumb from 'common/CustomBreadcrumb/CustomBreadcrumb';
import FormSelectSwitchCase, { FORM_SELECT_CASE_TYPE } from 'common/FormSelect/FormSelectSwitchCase';
import FormSelectMultipleNormal from 'common/FormSelectMultipleNormal/FormSelectMultipleNormal';
import { customizeRequiredMark } from 'common/LabelDefault/LabelDefault';
import ModalSuccess from 'common/Modal/ModalSuccess/ModalSuccess';
import { B5Regular, B6Regular, HeaderBold24 } from 'common/Text.styles';

import { DELEGATE_PERMISSION_KEY } from 'constants/Setting/Permission/delegatePermission.constant';
import color from 'constants/color.constant';
import { SERVICE } from 'constants/service.constant';
import type { ITFInitiativeRouteParams } from 'types/InitiativeRouteParams.type';
import type { ITFDelegatePermissionForm } from 'types/Setting/Permission/DelegatePermissionForm.type';

import { validateMaxByte } from 'helper/Validators';
import { useCommon } from 'hooks/common/useCommon';
import { useUserInfo } from 'hooks/common/useUserInfo';
import { useDelegatePermissionDetail } from 'hooks/setting/permission/useDelegatePermissionDetail';
import { useOptionsDelegatePermission } from 'hooks/setting/permission/useOptionDelegatePermission';
import { setSelectedCompany } from 'store/slice/setting/master-data/generalMemo';
import type { SettingDelegatePermissionDetailState } from 'store/slice/setting/permission/delegatePermissionDetail';
import { setSelectedCustomer, settingDelegatePermissionDetailSelector } from 'store/slice/setting/permission/delegatePermissionDetail';
import type { RootState } from 'store/store';
import { useAppDispatch } from 'store/store';
import { BodyCreateBy, Container, CustomB5Regular, CustomBH5SemiBold, SubContainer } from './DelegatePermissionDetail.styles';

export const DelegatePermissionDetailComponent = () => {
    const dispatch = useAppDispatch();
    const [form] = Form.useForm<ITFDelegatePermissionForm>();

    const history = useHistory();
    const params = useParams<ITFInitiativeRouteParams>();
    const referenceNo = Form.useWatch('memoCode', form) ?? '';

    const { t } = useTranslation();
    const {
        isEdit,
        onSubmit,
        fetchDelegatePermissionDetail,
        onUpdate,
        handleChangeOptions,
        dirty,
        setDirty,
        onSelectStage,
        dataDelegatePermission,
        onSelectFlow,
        onSelectSubstitute,
        dataApproverList,
        duplicatedUsernames,
    } = useDelegatePermissionDetail(form);
    const { isShowSuccess, hideSuccess } = useCommon(DELEGATE_PERMISSION_KEY.SUBMIT);

    const delegatePermissionState = useSelector<RootState, SettingDelegatePermissionDetailState>(settingDelegatePermissionDetailSelector);

    const { info } = useUserInfo();
    const fullName = `${info?.firstname} ${info?.surname}`;

    const { selectedStage, selectedFlow, selectedSubstitute } = delegatePermissionState;

    const { optionsStage } = useOptionsDelegatePermission();

    const itemsBreadcrumb = [
        {
            title: <Link to="/settings">{t('Setting')}</Link>,
        },
        {
            title: <Link to="/setting/permission">{t('Permission')}</Link>,
        },
        {
            title: <Link to="/setting/permission/delegate-permission">{t('Delegate Permission')}</Link>,
        },
    ];

    const handleOnSubmitDelegatePermission = async (action: string) => {
        try {
            await form.validateFields();
            const values = form.getFieldsValue();
            if (isEdit) {
                onUpdate(values, action);
            } else {
                onSubmit(values, action);
            }
        } catch (error) {
            console.log('error:', error);
        }
    };

    const handleWordingSuccess = () => {
        return isEdit ? t('Delegate Permission has been updated') : t('Delegate Permission has been created');
    };

    const padZero = (num: number): string => {
        return num < 10 ? `0${num}` : `${num}`;
    };

    const formatDateTimeLocal = (date: Date): string => {
        const day: string = padZero(date.getDate());
        const month: string = padZero(date.getMonth() + 1);
        const year: number = date.getFullYear();
        const hours: string = padZero(date.getHours());
        const minutes: string = padZero(date.getMinutes());

        return `${day}/${month}/${year} ${hours}:${minutes}`;
    };
    const todayDateTime: Date = new Date();
    const formattedDateTime: string = formatDateTimeLocal(todayDateTime);
    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}`;
    };

    useEffect(() => {
        if (isEdit && params.id) {
            fetchDelegatePermissionDetail();
        }
    }, [isEdit, params.id]);


    useEffect(() => {
        return () => {
            dispatch(setSelectedCompany({}));
            dispatch(setSelectedCustomer({}));
        };
    }, []);

    const options: SelectProps['options'] = [];
    const selectedApproversRaw = form.getFieldValue('searchApprover') ?? [];
    const selectedApproverValues = selectedApproversRaw.map((s: any) => s?.value ?? s); // normalize เป็น string[]

    dataApproverList.forEach((item) => {
        const username = item.username ?? '';
        const userId = item.user_id ?? '';
        const fullname = `${item.firstname ?? ''} ${item.surname ?? ''}`.trim();
        const isChecked = selectedApproverValues.includes(username);

        options.push({
            value: username,
            label: (
                <Row
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                        const alreadySelected = isChecked;
                        const newSelected = alreadySelected
                            ? selectedApproverValues.filter((u: string) => u !== username)
                            : [...selectedApproverValues, username];

                        form.setFieldsValue({ searchApprover: newSelected });
                        setDirty(false);
                    }}
                >
                    <Checkbox
                        checked={isChecked}
                        onClick={(e) => {
                            if (dirty) e.stopPropagation();
                            setDirty(false);
                        }}
                    >
                        <B6Regular style={{ color: 'unset' }}>{`${userId} - ${fullname}`}</B6Regular>
                    </Checkbox>
                </Row>
            ),
        });
    });

    const showCreateBy = (isEdit: boolean) => {
        if (isEdit) {
            return (
                <BodyCreateBy>
                    <Row>
                        <CustomB5Regular>Created by</CustomB5Regular>
                        <CustomB5Regular>:</CustomB5Regular>
                        {dataDelegatePermission?.createdName && (
                            <CustomBH5SemiBold>{dataDelegatePermission?.createdName}</CustomBH5SemiBold>
                        )}
                        <CustomB5Regular>-</CustomB5Regular>
                        {dataDelegatePermission?.createdAt && <B5Regular>{formatDate(dataDelegatePermission?.createdAt ?? '')}</B5Regular>}
                    </Row>
                    <Row>
                        <CustomB5Regular>Updated by</CustomB5Regular>
                        <CustomB5Regular>:</CustomB5Regular>
                        {dataDelegatePermission?.updatedName && (
                            <CustomBH5SemiBold>{dataDelegatePermission?.updatedName}</CustomBH5SemiBold>
                        )}
                        <CustomB5Regular>-</CustomB5Regular>
                        {dataDelegatePermission?.updatedAt && <B5Regular>{formatDate(dataDelegatePermission?.updatedAt ?? '')}</B5Regular>}
                    </Row>
                </BodyCreateBy>
            );
        }
        return (
            <BodyCreateBy>
                <Row>
                    <CustomB5Regular>Created by</CustomB5Regular>
                    <CustomB5Regular>:</CustomB5Regular>
                    <CustomBH5SemiBold>{fullName}</CustomBH5SemiBold>
                    <CustomB5Regular>-</CustomB5Regular>
                    <B5Regular>{formattedDateTime}</B5Regular>
                </Row>
            </BodyCreateBy>
        );
    };

    return (
        <Container>
            <SubContainer>
                <Form
                    form={form}
                    layout="vertical"
                    scrollToFirstError={{
                        block: 'center',
                    }}
                    requiredMark={customizeRequiredMark}
                >
                    <CustomBreadcrumb items={itemsBreadcrumb} separator=">" />
                    <Divider className="divider" />
                    <Row style={{ alignItems: 'center' }}>
                        <HowToRegOutlined className="icon" />
                        <HeaderBold24 className="title">{t('Delegate Permission')}</HeaderBold24>
                    </Row>
                    <Card className="card-information">
                        <div>
                            <Row gutter={[80, 20]}>
                                <Col span={8}>
                                    <Form.Item
                                        label={t('Stage')}
                                        name="stage"
                                        rules={[{ required: true, message: `${t('Stage')} ${t('is required')}` }]}
                                    >
                                        <Select
                                            disabled={isEdit}
                                            id="stageName"
                                            placeholder={t('Choose Stage')}
                                            options={optionsStage}
                                            onChange={onSelectStage}
                                            value={selectedStage}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </div>
                        <div>
                            <Row gutter={[80, 20]}>
                                <Col span={8}>
                                    <Form.Item
                                        name="flowName"
                                        label={t('Flow Name')}
                                        rules={[{ required: true, message: `${t('Flow Name')} ${t('is required')}` }]}
                                    >
                                        <FormSelectSwitchCase
                                            caseType={FORM_SELECT_CASE_TYPE.FLOW_NAME}
                                            id="flowName"
                                            name="flowName"
                                            onSelect={onSelectFlow}
                                            disabled={isEdit || !selectedStage}
                                            placeholder={t('Choose Flow Name')}
                                            modalProps={{
                                                title: t('Flow Name'),
                                            }}
                                            params={{
                                                stageName: selectedStage,
                                            }}
                                            apiService={SERVICE.MASTER_DATA}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={16}>
                                    <Form.Item
                                        label={t('Activity Name')}
                                        name="activityName"
                                        rules={[
                                            { required: true, message: `${t('Activity Name')} ${t('is required')}` },
                                            () => ({
                                                validator: (_, val) => {
                                                    return validateMaxByte(val, 200, t('Activity Name'));
                                                },
                                            }),
                                        ]}
                                    >
                                        <Input disabled={isEdit || !selectedStage} />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </div>
                        <div>
                            <Row gutter={[80, 20]}>
                                <Col span={8}>
                                    <FormSelectSwitchCase
                                        caseType={FORM_SELECT_CASE_TYPE.USER}
                                        disabled={!selectedFlow || isEdit}
                                        required={true}
                                        label={t('Substitute Name')}
                                        id="searchSubstitute"
                                        name="searchSubstitute"
                                        onSelect={onSelectSubstitute}
                                        modalProps={{
                                            title: t('Substitute'),
                                        }}
                                        params={{
                                            companyCode: info?.company_code,
                                        }}
                                        placeholder={t('Choose Substitute Name')}
                                        apiService={SERVICE.MASTER_DATA}
                                    />
                                </Col>
                                <Col span={16}>
                                    <FormSelectMultipleNormal
                                        label={t('Approver Name')}
                                        id="searchApprover"
                                        name="searchApprover"
                                        options={options}
                                        onChange={handleChangeOptions}
                                        disabled={!selectedSubstitute}
                                        required={true}
                                        placeholder={t('Choose Approver Name')}
                                        duplicatedUsernames={duplicatedUsernames}
                                    />
                                </Col>
                            </Row>
                        </div>
                        <div style={{ paddingRight: 80 }}>
                            <Row gutter={[42, 0]}>
                                <Col span={4}>
                                    <Form.Item label="Start Date" name="startDate" rules={[{ required: true }]}>
                                        <DatePicker
                                            disabled={!selectedSubstitute?.username}
                                            format="DD/MM/YYYY"
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={4}>
                                    <Form.Item label="Time" name="startTime" rules={[{ required: true }]}>
                                        <TimePicker disabled={!selectedSubstitute?.username} format="HH:mm" style={{ width: '100%' }} />
                                    </Form.Item>
                                </Col>
                                <Col span={4} style={{ marginLeft: 40 }}>
                                    <Form.Item label="End Date" name="endDate" rules={[{ required: true }]}>
                                        <DatePicker
                                            disabled={!selectedSubstitute?.username}
                                            format="DD/MM/YYYY"
                                            style={{ width: '100%' }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={4}>
                                    <Form.Item label="Time" name="endTime" rules={[{ required: true }]}>
                                        <TimePicker disabled={!selectedSubstitute?.username} format="HH:mm" style={{ width: '100%' }} />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </div>
                    </Card>
                    {showCreateBy(isEdit)}
                </Form>
            </SubContainer>

            <Row justify="space-between">
                <Row>
                    <ButtonPrimaryOutline
                        text={t('Back')}
                        icon={<LeftOutlined />}
                        style={{
                            paddingRight: 18,
                        }}
                        size="small"
                        onClick={() => {
                            history.push('/setting/permission/delegate-permission');
                        }}
                    />
                </Row>
                <Row style={{ gap: 10 }}>
                    <ButtonPrimary
                        text={t('Submit & New Item')}
                        icon={<CheckCircleOutlined />}
                        onClick={() => handleOnSubmitDelegatePermission(DELEGATE_PERMISSION_KEY.SUBMIT_AND_NEW)}
                        htmlType="button"
                        size="small"
                    />
                    <ButtonPrimary
                        text={t('Submit')}
                        icon={<CheckCircleOutlined />}
                        onClick={() => handleOnSubmitDelegatePermission(DELEGATE_PERMISSION_KEY.SUBMIT)}
                        htmlType="button"
                        size="small"
                    />
                </Row>
            </Row>
            <ModalSuccess
                open={isShowSuccess}
                onOk={() => {
                    history.push('/setting/permission/delegate-permission');
                    hideSuccess();
                }}
                title={handleWordingSuccess()}
                referenceNo={referenceNo}
                icon={<HowToRegOutlined style={{ fontSize: 72, color: color.SecondaryColor }} />}
            />
        </Container>
    );
};

export default DelegatePermissionDetailComponent;
