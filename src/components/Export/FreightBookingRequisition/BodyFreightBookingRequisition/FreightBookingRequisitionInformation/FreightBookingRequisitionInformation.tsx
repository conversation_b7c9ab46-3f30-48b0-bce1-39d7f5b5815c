import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Col, Form, Input, Row } from 'antd';

import { RowWrapper } from 'components/Export/CreditRequisition/BodyCreditRequisition/CreditRequisition/CreditRequisition';

import type { ITFFreightBookingRequisitionForm } from 'types/Export/Freight/FreightBookingRequisitionForm.type';

import { getSelectedLabel } from 'helper/FormatField';
import { validateMaxByte } from 'helper/Validators';
import { freightBookingRequisitionSelector } from 'store/slice/export/freight-booking-requisition/freightBookingRequisition';

import { ContainerForm } from './FreightBookingRequisitionInformation.styles';

import dayjs from 'dayjs';

interface ITFProps {
    disabled: boolean;
    required: boolean;
}

const dateFormat = 'DD/MM/YYYY';

const FreightBookingRequisitionInformation = (props: ITFProps) => {
    const { t } = useTranslation();

    const form = Form.useFormInstance<ITFFreightBookingRequisitionForm>();

    const dataFreightBookingInfo = useSelector(freightBookingRequisitionSelector).dataResponse?.freightBookingInfo;

    useEffect(() => {
        form.setFieldsValue({
            freightCompanyName: getSelectedLabel(dataFreightBookingInfo?.companyCode, dataFreightBookingInfo?.companyNameEng),
            freightOrganization: getSelectedLabel(dataFreightBookingInfo?.orgCode, dataFreightBookingInfo?.orgNameEng),
            freightStatus: dataFreightBookingInfo?.bookingStatus,
            freightRequestDate: dataFreightBookingInfo?.requestDate
                ? dayjs(dataFreightBookingInfo.requestDate).format(dateFormat)
                : dayjs().format(dateFormat),
            freightInvoiceNo: dataFreightBookingInfo?.invoiceNo,
            freightShipmentNo: dataFreightBookingInfo?.shipmentNo,
            freightConsigneeName: getSelectedLabel(dataFreightBookingInfo?.consigneeCode, dataFreightBookingInfo?.consigneeName),
            freightCoLoadInvoice: dataFreightBookingInfo?.coLoadInvoice,
            freightCoLoadType: dataFreightBookingInfo?.coLoadType,
        });
    }, [form, dataFreightBookingInfo]);

    return (
        <ContainerForm>
            <RowWrapper>
                <Col span={24} md={12} lg={8}>
                    <Form.Item label={`${t('Company Name')}`} name="freightCompanyName">
                        <Input disabled></Input>
                    </Form.Item>
                </Col>
                <Col span={24} md={12} lg={8}>
                    <Form.Item label={`${t('Organization')}`} name="freightOrganization">
                        <Input disabled></Input>
                    </Form.Item>
                </Col>
                <Col span={24} md={12} lg={8}>
                    <Row gutter={10}>
                        <Col span={12} lg={12}>
                            <Form.Item label={`${t('Booking Status')}`} name="freightStatus">
                                <Input disabled></Input>
                            </Form.Item>
                        </Col>
                        <Col span={12} lg={12}>
                            <Form.Item label={`${t('Requisition Date')}`} name="freightRequestDate">
                                <Input disabled></Input>
                            </Form.Item>
                        </Col>
                    </Row>
                </Col>
            </RowWrapper>

            <RowWrapper>
                <Col span={24} md={12} lg={8}>
                    <Form.Item label={`${t('Invoice No.')}`} name="freightInvoiceNo">
                        <Input disabled></Input>
                    </Form.Item>
                </Col>
                <Col span={24} md={12} lg={8}>
                    <Form.Item label={`${t('Shipment No.')}`} name="freightShipmentNo">
                        <Input disabled></Input>
                    </Form.Item>
                </Col>
                <Col span={24} md={12} lg={8}>
                    <Form.Item label={`${t('Consignee Name')}`} name="freightConsigneeName">
                        <Input disabled></Input>
                    </Form.Item>
                </Col>
            </RowWrapper>

            <RowWrapper>
                <Col span={24} md={16} lg={16}>
                    <Form.Item
                        label={`${t('Co-Load Invoice (from Plan Load)')}`}
                        name="freightCoLoadInvoice"
                        rules={[
                            {
                                whitespace: true,
                                message: `${t('Co-Load Invoice')} ${t('can not whitespace')}`,
                            },
                            () => ({
                                validator: (_, val) => {
                                    return validateMaxByte(val, 100, t('Co-Load Invoice'));
                                },
                            }),
                        ]}
                    >
                        <Input
                            onChange={(e) => {
                                if (e.target.value === '') {
                                    form.setFieldsValue({
                                        freightCoLoadType: null,
                                    });
                                }
                            }}
                        ></Input>
                    </Form.Item>
                </Col>
            </RowWrapper>
        </ContainerForm>
    );
};
export default FreightBookingRequisitionInformation;
