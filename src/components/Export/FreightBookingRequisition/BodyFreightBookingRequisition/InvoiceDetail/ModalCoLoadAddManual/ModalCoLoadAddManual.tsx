import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { ArticleOutlined } from '@mui/icons-material';
import { Col, Form, Input, Row } from 'antd';

import { ButtonPrimary, ButtonPrimaryOutline } from 'common/Button';
import { CustomModal } from 'common/CustomModal/CustomModal';
import { Numeric } from 'common/InputNumber';
import { customizeRequiredMark } from 'common/LabelDefault/LabelDefault';
import ModalError from 'common/Modal/Error/ModalError';
import SpinLoading from 'common/SpinLoading/SpinLoading';
import { TitleSemiBold22 } from 'common/Text.styles';

import { FREIGHT_BOOKING_LOADING_KEY } from 'constants/Export/FreightBookingRequisition/freight.constant';
import { TEMPERATURE_UNIT } from 'constants/container.constant';
import type { ITFCoLoadAddManualFieldType } from 'types/Export/Freight/InvoiceDetail.type';

import { maxByte } from 'helper/Validators';
import { useCommon } from 'hooks/common/useCommon';
import { useCoLoadAddManual } from 'hooks/export/freight/useCoLoadAddManual';
import {
    freightBookingReqInvoiceDetailSelector,
    setKindOfPackagesList,
} from 'store/slice/export/freight-booking-requisition/freightBookingReqInvoiceDetail';

import { CustomErrorMessage } from '../FreightBookingRequisitionInvoiceDetail.style';
import TableModalCoLoadAddManual from './TableModalCoLoadAddManual';

interface ModalCoLoadAddManualProps {
    disabled?: boolean;
    readOnly?: boolean;
}

const ModalCoLoadAddManual = (props: ModalCoLoadAddManualProps) => {
    const { disabled, readOnly = false } = props;
    const { t } = useTranslation();
    const dispatch = useDispatch();

    const [formInstance] = Form.useForm();
    const form = formInstance;
    const { isShowError, hideError, isShowLoading: isShowLoadingCreate } = useCommon(FREIGHT_BOOKING_LOADING_KEY.SUBMIT_CO_LOAD_ADD_MANUAL);

    const { onCancel, onFinish, transform, setIsEdit, setIsSubmit, initialData, isSubmit, isEdit, addRow } = useCoLoadAddManual(
        false,
        form,
    );
    const list = useSelector(freightBookingReqInvoiceDetailSelector).list;
    const { isShowModalCoLoadAddManual, dataEditCoLoadAddManual } = useSelector(freightBookingReqInvoiceDetailSelector);

    useEffect(() => {
        if (dataEditCoLoadAddManual) {
            setIsEdit({ isEdit: true, idEdit: dataEditCoLoadAddManual.id });
            form.setFieldsValue({
                invoiceNo: dataEditCoLoadAddManual.invoiceNo,
                grossWeight: dataEditCoLoadAddManual.grossWeight,
                cubicMeters: dataEditCoLoadAddManual.cubicMeters,
            });
            dispatch(
                setKindOfPackagesList(
                    transform(
                        dataEditCoLoadAddManual.packageDetail.map((item) => {
                            return {
                                productDescription: `${item.productDescription ?? ''}`,
                                packQuantity: `${item.plPackageQuantity}`,
                                unit: `${item.unit}`,
                                isDuplicate: false,
                            };
                        }),
                    ),
                ),
            );
        }
    }, [dataEditCoLoadAddManual]);

    useEffect(() => {
        if (isShowModalCoLoadAddManual && !dataEditCoLoadAddManual) {
            addRow(list);
        }
    }, [dataEditCoLoadAddManual, isShowModalCoLoadAddManual]);

    return (
        <>
            <CustomModal
                cancelButtonProps={{ id: 'buttonCloseContainer' }}
                footer={false}
                maskClosable={false}
                destroyOnClose={true}
                open={isShowModalCoLoadAddManual}
                onOk={onFinish}
                onCancel={() => {
                    onCancel();
                    setIsEdit(initialData.initIsEdit);
                }}
                centered
                size="large"
            >
                <SpinLoading isShow={isShowLoadingCreate}>
                    <Form
                        onFinish={onFinish}
                        form={form}
                        layout="vertical"
                        preserve={false}
                        initialValues={{
                            unitTemperature: TEMPERATURE_UNIT.CELSIUS,
                        }}
                        disabled={disabled}
                        autoComplete="off"
                        requiredMark={customizeRequiredMark}
                    >
                        <Row className="row-title">
                            <ArticleOutlined className="icon" />
                            <TitleSemiBold22 className="title" id="freight_containerPopuptitle">
                                {t('Create Invoice')}
                            </TitleSemiBold22>
                        </Row>

                        <Row gutter={30}>
                            <Col span={12}>
                                <CustomErrorMessage>
                                    <Form.Item<ITFCoLoadAddManualFieldType>
                                        label={t('Invoice No.')}
                                        name="invoiceNo"
                                        rules={[
                                            {
                                                whitespace: true,
                                                required: true,
                                                message: `${t('Invoice No.')} ${t('is required')}`,
                                            },
                                            {
                                                validator(_, value) {
                                                    if (maxByte(value, 30)) {
                                                        return Promise.reject(new Error(`${t('Invoice No.')} ${t('is too long')}`));
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <Input disabled={disabled} id="customs_CompanyName"></Input>
                                    </Form.Item>
                                </CustomErrorMessage>
                            </Col>
                            <Col span={12}>
                                <Row gutter={10} style={{ marginRight: '20px' }}>
                                    <Col span={12}>
                                        <CustomErrorMessage>
                                            <Form.Item<ITFCoLoadAddManualFieldType>
                                                label={t('Gross Weight (KG)')}
                                                name="grossWeight"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: `${t('Gross Weight (KG)')} ${t('is required')}`,
                                                    },
                                                ]}
                                            >
                                                <Numeric
                                                    id="customs_ExpenseFreightAmount"
                                                    decimalScale={5}
                                                    onChange={() => {}}
                                                    disable={disabled}
                                                    placeholder={'0.00000'}
                                                    isAllowed={(inputObj: any) => {
                                                        const { value } = inputObj;
                                                        const maxValue = Math.pow(10, 10);
                                                        return value >= 0 && value < maxValue;
                                                    }}
                                                />
                                            </Form.Item>
                                        </CustomErrorMessage>
                                    </Col>
                                    <Col span={12}>
                                        <CustomErrorMessage>
                                            <Form.Item<ITFCoLoadAddManualFieldType>
                                                label={t('Cubic Meters (CBM)')}
                                                name="cubicMeters"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: `${t('Cubic Meters (CBM)')} ${t('is required')}`,
                                                    },
                                                ]}
                                            >
                                                <Numeric
                                                    id="customs_ExpenseFreightAmount"
                                                    decimalScale={2}
                                                    onChange={() => {}}
                                                    disable={disabled}
                                                    placeholder={'0.00'}
                                                    isAllowed={(inputObj: any) => {
                                                        const { value } = inputObj;
                                                        const maxValue = Math.pow(10, 10);
                                                        return value >= 0 && value < maxValue;
                                                    }}
                                                />
                                            </Form.Item>
                                        </CustomErrorMessage>
                                    </Col>
                                </Row>
                            </Col>
                        </Row>

                        <TableModalCoLoadAddManual form={form} isSubmit={isSubmit.isSubmit} disabled={disabled} readOnly={readOnly} />

                        <Row justify={'end'} style={{ marginTop: '20px', gap: '10px' }}>
                            <ButtonPrimaryOutline
                                text={t('Close')}
                                id="freight_containerPopupbuttonClose"
                                onClick={() => {
                                    onCancel();
                                    setIsEdit(initialData.initIsEdit);
                                }}
                                icon={<CloseCircleOutlined />}
                                size="small"
                                disabled={isShowLoadingCreate}
                            />
                            <ButtonPrimary
                                text={t('Submit & New Item')}
                                id="freight_containerPopupbuttonSubmit"
                                icon={<CheckCircleOutlined />}
                                htmlType="submit"
                                size="small"
                                hidden={disabled}
                                disabled={isShowLoadingCreate}
                                onClick={() => {
                                    setIsSubmit({
                                        isSubmit: true,
                                        type: 'submitAndNew',
                                    });
                                }}
                            />
                            <ButtonPrimary
                                text={t('Submit')}
                                id="freight_containerPopupbuttonSubmit"
                                icon={<CheckCircleOutlined />}
                                htmlType="submit"
                                size="small"
                                hidden={disabled}
                                disabled={isShowLoadingCreate}
                                onClick={() => {
                                    setIsSubmit({
                                        isSubmit: true,
                                        type: 'submit',
                                    });
                                }}
                            />
                        </Row>
                    </Form>
                </SpinLoading>
            </CustomModal>

            <ModalError
                isModalVisible={isShowError}
                onCancel={hideError}
                onOk={hideError}
                title={isEdit.isEdit ? t('Error Update Create Invoice') : t('Error Submit Create Invoice')}
                showSupport={false}
            />
        </>
    );
};

export default ModalCoLoadAddManual;
