import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { DeleteOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';
import { Form, Input, Row, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';

import { ButtonDangerOutline, ButtonPrimaryOutlineDashed } from 'common/Button';
import CustomTable from 'common/CustomTable/CustomTable';
import FormSelectUnit from 'common/FormSelect/FormSelectUnit';
import { Numeric } from 'common/InputNumber';
import ModalDeleteMain from 'common/Modal/DeleteMain/ModalDelete';
import { B4Regular, DisplaySemiBold18 } from 'common/Text.styles';

import colorConstant from 'constants/color.constant';
import { SERVICE } from 'constants/service.constant';
import type { DataKindOfPackages, DataTableKindOfPackages } from 'types/Export/Freight/InvoiceDetail.type';

import { formatter, parser } from 'helper/FormatNumber';
import { validateMaxByte } from 'helper/Validators';
import { useCoLoadAddManual } from 'hooks/export/freight/useCoLoadAddManual';

import { CustomErrorMessage } from '../FreightBookingRequisitionInvoiceDetail.style';

interface ITFProps {
    form: FormInstance;
    isSubmit: boolean;
    disabled?: boolean;
    readOnly: boolean;
}
const TableModalCoLoadAddManual = (props: ITFProps) => {
    const { form, isSubmit, disabled, readOnly } = props;
    const { t } = useTranslation();
    const { rowSelection, selectedRowKeys, showModalDelete, setShowModalDelete, onOkDelete, list, transform, handleSetValue, addRow } =
        useCoLoadAddManual(disabled ?? false, form);

    const columnAttribute = {
        onCell: () => ({
            style: { verticalAlign: 'top' },
        }),
    };

    const requireSign = (text: string) => {
        return (
            <span>
                {text}
                <span style={{ color: 'red' }}>*</span>
            </span>
        );
    };

    const columns: ColumnsType<DataTableKindOfPackages> = [
        {
            key: '0',
            title: t('No.'),
            dataIndex: 'no',
            width: 80,
            align: 'center',
            sorter: (a: any, b: any) => a.no - b.no,
        },
        Table.SELECTION_COLUMN,
        {
            key: '1',
            title: t('Product Description'),
            dataIndex: 'productDescription',
            width: 650,
            ...columnAttribute,
            render: (text: any, record: DataTableKindOfPackages, count: any) => {
                return (
                    <CustomErrorMessage style={{ textAlign: 'left' }}>
                        <Form.Item
                            id={`customsDoc_productDescription_${record.key}`}
                            name={`productDescription_${record.key}`}
                            rules={[
                                {
                                    whitespace: true,
                                },
                                () => ({
                                    validator: (_, val) => validateMaxByte(val, 300, t('Product Description')),
                                }),
                            ]}
                        >
                            <Input
                                id="customs_ExpenseFreightAmount"
                                disabled={disabled}
                                onBlur={(e) => {
                                    handleSetValue('productDescription', record, e.target.value);
                                }}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        e.preventDefault();
                                    }
                                }}
                            />
                        </Form.Item>
                    </CustomErrorMessage>
                );
            },
        },
        {
            key: '2',
            title: requireSign(t('Pack Quantity')),
            dataIndex: 'packQuantity',
            width: 150,
            ...columnAttribute,
            render: (text: any, record: DataTableKindOfPackages, count: any) => {
                return (
                    <CustomErrorMessage style={{ textAlign: 'left' }}>
                        <Form.Item
                            id={`planLoad_TermAndConDocumentName_${record.key}`}
                            name={`packQuantity_${record.key}`}
                            rules={[
                                {
                                    whitespace: true,
                                    required: true,
                                    message: t('Pack Quantity') + ' ' + t('is required'),
                                },
                                () => ({
                                    validator: (_, val) => validateMaxByte(val, 100, t('Pack Quantity')),
                                }),
                            ]}
                        >
                            <Numeric
                                id="customs_ExpenseFreightAmount"
                                decimalScale={5}
                                isAllowed={(inputObj: any) => {
                                    const { value } = inputObj;
                                    const maxValue = Math.pow(10, 10);
                                    return value >= 0 && value < maxValue;
                                }}
                                onBlur={(e) => {
                                    handleSetValue('packQuantity', record, e.target.value);
                                }}
                                onChange={(e) => {
                                    form.setFieldsValue({
                                        [`packQuantity_${record.key}`]: e.target.value.replace(/[\t\n]/g, ' '),
                                    });
                                }}
                                disable={disabled}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        e.preventDefault();
                                    }
                                }}
                            />
                        </Form.Item>
                    </CustomErrorMessage>
                );
            },
        },
        {
            key: '3',
            title: requireSign(t('Unit')),
            dataIndex: 'unit',
            width: 150,
            ...columnAttribute,
            render: (text: any, record: DataTableKindOfPackages, count: any) => {
                return (
                    <CustomErrorMessage>
                        <FormSelectUnit
                            label=""
                            id="freightNoOfPackageUnit"
                            name={`unit_${record.key}`}
                            disabled={disabled}
                            onSelect={(e) => {
                                if (e.unitCode) {
                                    handleSetValue('unit', record, e.unitCode);
                                    form.setFieldsValue({
                                        [`unit_${record.key}`]: e.unitCode,
                                    });
                                } else {
                                    handleSetValue('unit', record, undefined);
                                    form.setFieldsValue({
                                        [`unit_${record.key}`]: null,
                                    });
                                }
                            }}
                            apiService={SERVICE.FREIGHT}
                            prefixID="freight"
                            mode="select"
                            formItemProps={{
                                label: '',
                                id: 'freight_containerPopupContainerSize',
                                name: `unit_${record.key}`,
                                rules: [
                                    {
                                        whitespace: true,
                                        required: true,
                                        message: t('Unit') + ' ' + t('is required'),
                                    },
                                    () => ({
                                        validator: (_, val) =>
                                            val && record.isDuplicate ? Promise.reject(new Error(t(`Duplicate Unit`))) : Promise.resolve(),
                                    }),
                                ],
                            }}
                            placeholder={t('Choose Unit Type')}
                            selectInputProps={{
                                popupMatchSelectWidth: 150,
                            }}
                        />
                    </CustomErrorMessage>
                );
            },
        },
    ];

    const conditionShowPackages = (list: DataKindOfPackages[]) => {
        if (list.length === 0) return '';
        else if (list.length === 1 && list[0].unit) {
            return list[0].unit;
        } else if (list.every((item) => item.unit === list[0].unit)) {
            return list[0].unit;
        }
        return `Packages`;
    };

    const summary = useCallback(
        (pageData: readonly any[]) => {
            let totalPackQuantity = 0;
            pageData.forEach(({ packQuantity }) => {
                totalPackQuantity += Number(parser(packQuantity));
            });

            return (
                <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={2}></Table.Summary.Cell>
                    <Table.Summary.Cell index={1}>
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'flex-end',
                                alignItems: 'center',
                            }}
                        >
                            {t('Total')}
                        </div>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2}>
                        {
                            <div
                                style={{
                                    display: 'flex',
                                    justifyContent: 'flex-end',
                                    alignItems: 'center',
                                }}
                            >
                                {formatter(`${totalPackQuantity}`, 5) ?? '0.00000'}
                            </div>
                        }
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3}>{conditionShowPackages(list)}</Table.Summary.Cell>
                </Table.Summary.Row>
            );
        },
        [list],
    );

    return (
        <div>
            <Row style={{ margin: '20px 0px', alignItems: 'center', justifyContent: 'space-between' }}>
                <DisplaySemiBold18>{t('No. and Kind of Packages')}</DisplaySemiBold18>

                <ButtonDangerOutline
                    id={'buttonDeleteInstruction'}
                    text={t('Delete')}
                    onClick={() => setShowModalDelete(true)}
                    icon={<DeleteOutlined className="icon-delete" />}
                    disabled={!selectedRowKeys.length}
                    hidden={disabled}
                    size="small"
                />
            </Row>

            <CustomTable
                columns={columns}
                dataSource={transform(list)}
                pagination={false}
                rowSelection={rowSelection}
                rowClassName={(record: any, index: number) => (index % 2 === 0 ? 'table-row-light' : 'table-row-dark')}
                summary={summary}
                locale={{
                    emptyText:
                        isSubmit && list.length < 1 ? (
                            <B4Regular style={{ color: colorConstant.ErrorColor }}>{t('The item must be added to the list')}</B4Regular>
                        ) : undefined,
                }}
                scroll={{ y: 300 }}
            />

            <ButtonPrimaryOutlineDashed
                style={{ marginTop: '10px' }}
                block
                size="small"
                text={t('Add Row')}
                icon={<span>+</span>}
                id="buttonAddRowDocumentTermAndConPlanLoad"
                onClick={() => addRow(list)}
                disabled={disabled}
                hidden={readOnly}
            ></ButtonPrimaryOutlineDashed>
            <ModalDeleteMain
                totalItems={selectedRowKeys.length}
                setShowModalDelete={setShowModalDelete}
                showModalDelete={showModalDelete}
                onOkDelete={onOkDelete}
                titleId={'titleDeleteInstructionPopup'}
                descriptionId={'descriptionDeleteInstructionPopup'}
                btnCancelId={'buttonCloseDeleteInstructionPopup'}
                btnDeleteId={'buttonConfirmDeleteInstructionPopup'}
            />
        </div>
    );
};
export default TableModalCoLoadAddManual;
