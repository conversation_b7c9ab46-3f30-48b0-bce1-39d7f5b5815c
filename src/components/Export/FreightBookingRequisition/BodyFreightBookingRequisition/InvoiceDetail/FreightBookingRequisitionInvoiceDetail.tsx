import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Col, Form, Radio } from 'antd';

import { ButtonDangerOutline, ButtonPrimary } from 'common/Button';
import TableInvoiceDetail from 'common/InvoiceDetail/TableInvoiceDetail';
import ModalDeleteMain from 'common/Modal/DeleteMain/ModalDelete';

import { CO_LOAD_TYPE, FREIGHT_BOOKING_LOADING_KEY } from 'constants/Export/FreightBookingRequisition/freight.constant';
import type { ITFFreightBookingRequisitionForm } from 'types/Export/Freight/FreightBookingRequisitionForm.type';

import { useCommon } from 'hooks/common/useCommon';
import { useFreightBookingReqInvoiceDetail } from 'hooks/export/freight-booking-requisition/useFreightBookingReqInvoiceDetail';
import { freightBookingRequisitionSelector } from 'store/slice/export/freight-booking-requisition/freightBookingRequisition';

import ModalCoLoadAddManual from './ModalCoLoadAddManual';

interface ITFProps {
    disabled: boolean;
    required: boolean;
}

const FreightBookingRequisitionInvoiceDetail = (props: ITFProps) => {
    const { disabled, required } = props;
    const { t } = useTranslation();

    const form = Form.useFormInstance<ITFFreightBookingRequisitionForm>();
    const dataInvoiceDetail = useSelector(freightBookingRequisitionSelector).dataResponse?.invoiceDetail;
    const {
        onClickDeleteInvoiceDetail,
        selectedRowKeys,
        onClickShowModalCreateInvoice,
        onClickShowModalAddInvoice,
        isShowModalDeleteInvoiceDetail,
        setIsShowModalDeleteInvoiceDetail,
        onClickColoadType,
        rowSelection,
    } = useFreightBookingReqInvoiceDetail(disabled, form);

    const { isShowLoading: isShowLoadingDeleteInvoiceDetail } = useCommon(FREIGHT_BOOKING_LOADING_KEY.DELETE_FREIGHT_INVOICE_DETAIL);
    const freightCoLoadInvoice = Form.useWatch('freightCoLoadInvoice', form);
    const checkRequireColoadType: boolean = !!freightCoLoadInvoice;
    const dataFreightBookingInfo = useSelector(freightBookingRequisitionSelector).dataResponse?.freightBookingInfo;

    useEffect(() => {
        if (dataInvoiceDetail?.data?.length === 1 && !checkRequireColoadType) {
            form.resetFields(['freightCoLoadType']);
        }
    }, [dataInvoiceDetail]);

    useEffect(() => {
        form.setFieldsValue({
            freightCoLoadType: dataFreightBookingInfo?.coLoadType,
        });
    }, [dataFreightBookingInfo]);

    return (
        <>
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    marginBottom: '10px',
                    width: '100%',
                    justifyContent: 'flex-end',
                }}
            >
                <ButtonDangerOutline
                    id={'freight_invoice_detail_delete'}
                    text={t('Delete')}
                    onClick={() => setIsShowModalDeleteInvoiceDetail(true)}
                    icon={
                        <DeleteOutlined
                            style={{
                                fontSize: '17px',
                                height: '19px',
                                cursor: 'pointer',
                            }}
                        />
                    }
                    size="small"
                    disabled={!selectedRowKeys.length || disabled}
                    style={{ marginRight: '8px' }}
                />
                <ButtonPrimary
                    text={t('Create Invoice')}
                    id="buttonCreateInvoice"
                    onClick={onClickShowModalCreateInvoice}
                    icon={<PlusOutlined />}
                    htmlType="button"
                    size="small"
                    disabled={disabled}
                    style={{ marginRight: '8px' }}
                />
                <ButtonPrimary
                    text={t('Add Invoice')}
                    id="buttonAddMasterData"
                    onClick={onClickShowModalAddInvoice}
                    icon={<PlusOutlined />}
                    htmlType="button"
                    size="small"
                    disabled={disabled}
                    hidden={true}
                />
            </div>

            <TableInvoiceDetail
                disabled={disabled}
                isShowLoadingDeleteInvoiceDetail={isShowLoadingDeleteInvoiceDetail}
                form={form}
                dataInvoiceDetail={dataInvoiceDetail}
                rowSelection={rowSelection}
            ></TableInvoiceDetail>

            <Col
                span={24}
                md={12}
                lg={8}
                style={{
                    marginTop: '20px',
                }}
            >
                <Form.Item
                    label={t('Co-Load Type')}
                    name="freightCoLoadType"
                    rules={[
                        {
                            required: required && ((dataInvoiceDetail?.data?.length ?? 1) > 1 || checkRequireColoadType),
                            message: `${t('Co-Load Type')} ${t('is required')}`,
                        },
                    ]}
                >
                    <Radio.Group
                        disabled={disabled || (dataInvoiceDetail?.data?.length === 1 && !checkRequireColoadType)}
                        style={{ display: 'flex', columnGap: 40 }}
                    >
                        <Radio id="radioShipBySea" value={CO_LOAD_TYPE.COMBINE_BL} onClick={onClickColoadType}>
                            {t('Combine B/L')}
                        </Radio>
                        <Radio id="radioShipByAir" value={CO_LOAD_TYPE.SPLIT_BL} onClick={onClickColoadType}>
                            {t('Split B/L')}
                        </Radio>
                    </Radio.Group>
                </Form.Item>
            </Col>
            <ModalDeleteMain
                totalItems={selectedRowKeys.length}
                setShowModalDelete={setIsShowModalDeleteInvoiceDetail}
                showModalDelete={isShowModalDeleteInvoiceDetail}
                onOkDelete={onClickDeleteInvoiceDetail}
                titleId={'freight_invoice_detail_confirmDelete'}
                descriptionId={'freight_invoice_detail_pressConfirm'}
                btnCancelId={'freight_invoice_detail_buttonClose'}
                btnDeleteId={'freight_invoice_detail_buttonConfirm'}
            />
            <ModalCoLoadAddManual disabled={disabled} />
        </>
    );
};
export default FreightBookingRequisitionInvoiceDetail;
