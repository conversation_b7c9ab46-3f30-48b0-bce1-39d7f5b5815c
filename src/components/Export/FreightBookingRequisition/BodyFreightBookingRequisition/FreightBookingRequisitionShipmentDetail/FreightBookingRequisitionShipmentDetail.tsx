import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import { Col, DatePicker, Form, Input, Radio, Row, Select } from 'antd';

import CustomFormItemTextArea from 'common/CustomFormItemTextArea/CustomFormItemTextArea';
import FormRadioLoadingType from 'common/FormRadio/FormRadioLoadingType';
import FormSelectPaymentTerm from 'common/FormSelect/FormSelectPaymentTerm';
import FormSelectTransportationAgent from 'common/FormSelect/FormSelectTransportationAgent';
import FormSelectUnit from 'common/FormSelect/FormSelectUnit';
import FormSelectVendorGeneral from 'common/FormSelect/FormSelectVendorGeneral';
import ColLayout from 'common/InitiativeDetail/ColLayout';
import RowLayout from 'common/InitiativeDetail/RowLayout';
import { Numeric } from 'common/InputNumber';
import ShipmentStuffingPlace from 'common/ShipmentStuffingPlace/ShipmentStuffingPlace';

import { FREIGHT_MODE, FREIGHT_TERM } from 'constants/Export/FreightBookingRequisition/freight.constant';
import { SERVICE } from 'constants/service.constant';
import {
    AXLE_TYPE,
    CONTAINER_TYPE,
    FREIGHT_CHARGE,
    HANDLING_CHARGE,
    LOADING_TYPE,
    PRODUCT_TYPE,
    YES_NO,
} from 'constants/shipment.constant';
import type { ITFCyBilling } from 'types/Export/Freight/CyBilling.type';
import type { ITFFormFreightBookingReq } from 'types/Export/Freight/Form/FormFreightBookingRequisition.type';
import type { ITFFreightBookingRequisitionForm } from 'types/Export/Freight/FreightBookingRequisitionForm.type';
import type { ITFShipmentDetail } from 'types/Export/Freight/ShipmentDetail.type';
import type { ITFTableFreightShipmentStuffingPlace } from 'types/Export/Freight/StuffingPlace.type';
import type { ITFTransportationAgent } from 'types/TransportationAgent.type';

import { defaultEmpty } from 'helper/ConvertData';
import { convertToDayjs } from 'helper/ConvertDate';
import { isCBMContainer } from 'helper/Export/Container';
import { getSelectedLabel } from 'helper/FormatField';
import { validateMaxByte } from 'helper/Validators';
import { useFreightBookingReqShipmentDetail } from 'hooks/export/freight-booking-requisition/useFreightBookingReqShipmentDetail';
import { useFreightBookingReqShipmentStuffingPlace } from 'hooks/export/freight-booking-requisition/useFreightBookingReqShipmentStuffingPlace';
import { useModalFreightStuffingPlace } from 'hooks/export/freight/master/useModalFreightStuffingPlace';
import {
    freightBookingReqShipmentDetailSelector,
    setSelectdCyBilling,
    setSelectdTransportationAgent,
    setSelectedDestinationPort,
    setSelectedDischargePort,
    setSelectedIncoterm,
    setSelectedIntransitTo,
    setSelectedLoadingPort,
    setSelectedPaymentTerm,
    setSelectedTradeCommodity,
} from 'store/slice/export/freight-booking-requisition/freightBookingReqShipmentDetail';
import { freightBookingReqShipmentStuffingPlaceSelector } from 'store/slice/export/freight-booking-requisition/freightBookingReqShipmentStuffingPlace';
import {
    freightBookingRequisitionSelector,
    setPlaceAtDestination,
} from 'store/slice/export/freight-booking-requisition/freightBookingRequisition';

import { Container } from './FreightBookingRequisitionShipmentDetail.styles';

import SharedPlanLoadShipmentIncotermDetail from 'shared/Export/SharedPlanLoad/SharedPlanLoadShipmentIncotermDetail';
import SharedPlanLoadShipmentShipByDetail from 'shared/Export/SharedPlanLoad/SharedPlanLoadShipmentShipByDetail';
import SharedPlanLoadShipmentShippingDetail from 'shared/Export/SharedPlanLoad/SharedPlanLoadShipmentShippingDetail';

const { TextArea } = Input;

const { Option } = Select;

interface StructProps {
    disabled: boolean;
    required: boolean;
}

const FreightBookingRequisitionShipmentDetail = (props: StructProps) => {
    const { disabled, required } = props;
    const dispatch = useDispatch();

    const { t } = useTranslation();
    const form = Form.useFormInstance<ITFFreightBookingRequisitionForm>();
    const {
        tranform,
        onSelectTradeCommodity,
        onSelectLoadingPort,
        onSelectDischargePort,
        onSelectDestinationPort,
        onSelectIntransitTo,
        onSelectIncoterm,
        onSelectNoOfPackageUnit,
        onSelectPaymentTerm,
        onSelectTransportationAgent,
        onSelectCyBilling,
        requirePlaceAtDestination,
        requireStuffingPlace,
        onChangeLoadingType,
        loadingType,
        setLoadingType,
        onBlurGrossWeight,
        onBlurPlaceAtDestination,
    } = useFreightBookingReqShipmentDetail({
        form,
    });

    const { data: dataSourceStuffingPlace, latestKey } = useSelector(freightBookingReqShipmentStuffingPlaceSelector);
    const { dataResponse, action } = useSelector(freightBookingRequisitionSelector);
    const [isRequireCBM, setIsRequireCBM] = useState(false);
    const dataFreightBookingContainer = useSelector(freightBookingRequisitionSelector).freightBookingContainer;
    const dataFreightInfo = dataResponse?.freightBookingInfo;
    const dataFreightShipment = dataResponse?.shipmentDetail;
    const dataStuffingPlace = dataResponse?.shipmentStuffingPlace;

    const { defaultLimitStuffingPlace, fetchDataFreightStuffingPlace, transformStuffingPlace } = useModalFreightStuffingPlace();
    const { initialStuffingPlaces, onSelectStuffingPlace, onOkDeleteStuffingPlace, onIncreaseLatestKey } =
        useFreightBookingReqShipmentStuffingPlace(form);

    const { selectedIncoterm } = useSelector(freightBookingReqShipmentDetailSelector);

    const initialShipmentDataToStore = (dataFreightShipment: ITFShipmentDetail | undefined) => {
        dispatch(
            setSelectedTradeCommodity({
                commodityCode: dataFreightShipment?.commodityCode,
                commodityDesc: dataFreightShipment?.commodityDesc,
            }),
        );
        dispatch(
            setSelectedLoadingPort({
                portCode: dataFreightShipment?.loadingPortCode,
                portNameEng: dataFreightShipment?.loadingPortName,
                country: {
                    countryCode: dataFreightShipment?.loadingCountryCode,
                    countryNameEng: dataFreightShipment?.loadingCountryName,
                },
            }),
        );
        dispatch(
            setSelectedDischargePort({
                portCode: dataFreightShipment?.dischargePortCode,
                portNameEng: dataFreightShipment?.dischargePortName,
                country: {
                    countryCode: dataFreightShipment?.dischargeCountryCode,
                    countryNameEng: dataFreightShipment?.dischargeCountryName,
                },
            }),
        );
        dispatch(
            setSelectedDestinationPort({
                portCode: dataFreightShipment?.destinationPortCode,
                portNameEng: dataFreightShipment?.destinationPortName,
                country: {
                    countryCode: dataFreightShipment?.destinationCountryCode,
                    countryNameEng: dataFreightShipment?.destinationCountryName,
                },
            }),
        );
        dispatch(
            setSelectedIntransitTo({
                countryCode: dataFreightShipment?.intransitToCode,
                countryNameEng: dataFreightShipment?.intransitToName,
            }),
        );
        dispatch(
            setSelectedIncoterm({
                ...(dataFreightShipment?.incoterm ?? {}),
                incotermCode: dataFreightShipment?.incotermCode,
                incotermDesc: dataFreightShipment?.incoterm?.incotermDesc,
            }),
        );
        dispatch(
            setSelectedPaymentTerm({
                paymentTermCode: dataFreightShipment?.paymenttermCode,
                paymentTermNameEng: dataFreightShipment?.paymenttermNameEng,
                paymentTermNameTha: dataFreightShipment?.paymenttermNameTha,
            }),
        );
        dispatch(
            setSelectdTransportationAgent({
                ...(dataFreightShipment?.transportationAgent ?? ({} as ITFTransportationAgent)),
            }),
        );
        dispatch(
            setSelectdCyBilling({
                ...(dataFreightShipment?.cyBillingTo ?? ({} as ITFCyBilling)),
            }),
        );
    };

    useEffect(() => {
        const foundReeferInContainer = dataFreightBookingContainer?.data.some((item) => {
            return item.containerType === CONTAINER_TYPE.REEFER;
        });

        const setDefautGenSet = foundReeferInContainer ? YES_NO.YES : YES_NO.NO;

        form.setFieldsValue({ freightGenSet: setDefautGenSet });
    }, [dataFreightBookingContainer]);

    useEffect(() => {
        setLoadingType(dataFreightShipment?.loadingType ?? '');

        const foundReeferInContainer = dataResponse?.freightBookingContainer?.data.some((item) => {
            return item.containerType === CONTAINER_TYPE.REEFER;
        });

        const setDefautGenSet = foundReeferInContainer ? YES_NO.YES : YES_NO.NO;

        form.setFieldsValue({
            freightCommodity: getSelectedLabel(dataFreightShipment?.commodityCode, dataFreightShipment?.commodityDesc),
            freightShipBy: dataFreightShipment?.shipBy,
            freightProductType: dataFreightShipment?.productType,
            freightLoadingPort: dataFreightShipment?.loadingPortCode,
            freightLoadingPortName: dataFreightShipment?.loadingPortName,
            freightLoadingCountry: dataFreightShipment?.loadingCountryCode,
            freightLoadingCountryName: dataFreightShipment?.loadingCountryName,
            freightDischargePort: dataFreightShipment?.dischargePortCode,
            freightDischargePortName: dataFreightShipment?.dischargePortName,
            freightDischargeCountry: dataFreightShipment?.dischargeCountryCode,
            freightDischargeCountryName: dataFreightShipment?.dischargeCountryName,
            freightDestinationPort: dataFreightShipment?.destinationPortCode,
            freightDestinationPortName: dataFreightShipment?.destinationPortName,
            freightDestinationCountry: dataFreightShipment?.destinationCountryCode,
            freightDestinationCountryName: dataFreightShipment?.destinationCountryName,
            freightDimension: dataFreightShipment?.dimension,
            freightCubicMeters: defaultEmpty(dataFreightShipment?.cubicMeters),
            freightCubicUnit: dataFreightShipment?.cubicMetersUnit,
            freightNoOfPackage: defaultEmpty(dataFreightShipment?.noOfPackage),
            freightNoOfPackageUnit: dataFreightShipment?.noOfPackageUnit,
            freightMode: dataFreightShipment?.freightMode,
            freightTerm: dataFreightShipment?.freightTerm,
            freightGrossWeight: defaultEmpty(dataFreightShipment?.grossWeight),
            freightGrossWeightUnit: 'KG',
            freightIncoterm: dataFreightShipment?.incotermCode,
            freightIncotermPort: dataFreightShipment?.incotermPort,
            freightIntransitTo: dataFreightShipment?.intransitToCode,
            freightIntransitToName: dataFreightShipment?.intransitToName,
            freightPaymentTerm: getSelectedLabel(dataFreightShipment?.paymenttermCode, dataFreightShipment?.paymenttermNameEng),
            freightCharge: dataFreightShipment?.freightCharge ?? dataFreightShipment?.incoterm?.freightCharge,
            freightHandlingCharge: dataFreightShipment?.handlingCharge,
            freightCyStuffingDate: convertToDayjs(dataFreightShipment?.cyStuffingDate),
            freightStuffingDate: convertToDayjs(dataFreightShipment?.stuffingDate),
            freightReturnLoadDate: convertToDayjs(dataFreightShipment?.returnLoadDate),
            freightLoadingType: dataFreightShipment?.loadingType,
            freightTaxType: getSelectedLabel(dataFreightShipment?.taxTypeCode, dataFreightShipment?.taxTypeName),
            freightGenSet: dataFreightShipment?.genSet ?? setDefautGenSet,
            freightAxleType: dataFreightShipment?.axleType,
            freightTransportationAgent: getSelectedLabel(
                dataFreightShipment?.transportationAgentCode,
                dataFreightShipment?.transportationAgent?.agentNameEng,
            ),
            freightFactortContact: dataFreightShipment?.factoryContactCode,
            freightCyBillingTo: getSelectedLabel(dataFreightShipment?.cyBillingToCode, dataFreightShipment?.cyBillingTo?.vendorNameEng),
            freightCyRemark: dataFreightShipment?.cyRemark,
            freightRequisitionRemark: dataFreightShipment?.requisitionRemark,
            freightAdditionalConditionFromPlanload: dataFreightInfo?.additionalCondition,
            freightShipmentRemark: dataFreightShipment?.remark,
            freightPlaceAtDestination: dataFreightShipment?.placeAtDestination,
            freightEtdDate: convertToDayjs(dataFreightInfo?.etdDatetime),
            freightEtaDate: convertToDayjs(dataFreightInfo?.etaDatetime),
        });
        dispatch(setPlaceAtDestination(form?.getFieldValue('freightPlaceAtDestination')));
        initialShipmentDataToStore(dataFreightShipment);
    }, [form, dataFreightShipment, dataFreightInfo, dataResponse?.freightBookingContainer]);

    useEffect(() => {
        initialStuffingPlaces(tranform(dataStuffingPlace?.data ?? []));
        return () => {
            initialStuffingPlaces([]);
        };
    }, [dataStuffingPlace]);

    useEffect(() => {
        if (!requirePlaceAtDestination) {
            form?.validateFields(['freightPlaceAtDestination']);
        }
    }, [requirePlaceAtDestination]);

    useEffect(() => {
        const foundContainerCodeISDCB = dataFreightBookingContainer?.data.some((item) => {
            return isCBMContainer(item.containerCode ?? '');
        });
        setIsRequireCBM(foundContainerCodeISDCB);
    }, [dataFreightBookingContainer, form]);

    return (
        <>
            <Container>
                <RowLayout.ThreeCol>
                    <SharedPlanLoadShipmentShipByDetail<ITFFreightBookingRequisitionForm>
                        fields={{
                            shipmentCommodity: {
                                label: t('Trade Commodity'),
                                name: 'freightCommodity',
                                valueFieldName: 'freightCommodity',
                                required: false,
                                disabled: disabled,
                                onSelect: onSelectTradeCommodity,
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'freight',
                            },
                            shipmentShipBy: {
                                name: 'freightShipBy',
                                disabled: true,
                                required: false,
                            },
                        }}
                    />
                    <ColLayout.Third>
                        <Form.Item
                            label={`${t('Product Type')}`}
                            name="freightProductType"
                            rules={[
                                {
                                    required: required,
                                    message: `${t('Product Type')} ${t('is required')}`,
                                },
                            ]}
                        >
                            <Radio.Group>
                                <Radio id="freight_shipDetailradioProductTypeSIMPLE" value={PRODUCT_TYPE.SAMPLE}>{`${t(
                                    PRODUCT_TYPE.SAMPLE,
                                )}`}</Radio>
                                <Radio id="freight_shipDetailradioProductTypeNORMAL" value={PRODUCT_TYPE.NORMAL}>{`${t(
                                    PRODUCT_TYPE.NORMAL,
                                )}`}</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <SharedPlanLoadShipmentShippingDetail<ITFFreightBookingRequisitionForm>
                        concatIncotermPortConfig={{
                            incoterm: {
                                inputNameCode: 'freightIncoterm',
                                inputNamePort: 'freightIncotermPort',
                                requiredProcess: selectedIncoterm?.requirePortShippingProcess,
                            },
                            watchedFields: {
                                shipmentLoadingPort: true,
                                shipmentDestinationPort: true,
                                shipmentPlaceAtDestination: true,
                            },
                        }}
                        fields={{
                            shipmentLoadingPort: {
                                required: required,
                                disabled: disabled,
                                code: {
                                    name: 'freightLoadingPort',
                                    onSelect: onSelectLoadingPort,
                                },
                                name: {
                                    labelText: t('Loading Port Name'),
                                    name: 'freightLoadingPortName',
                                    disabled: true,
                                },
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'freight',
                            },
                            shipmentLoadingCountry: {
                                required: false,
                                disabled: true,
                                code: {
                                    name: 'freightLoadingCountry',
                                },
                                name: {
                                    name: 'freightLoadingCountryName',
                                },
                            },
                        }}
                    />
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <SharedPlanLoadShipmentShippingDetail<ITFFreightBookingRequisitionForm>
                        concatIncotermPortConfig={{
                            incoterm: {
                                inputNameCode: 'freightIncoterm',
                                inputNamePort: 'freightIncotermPort',
                                requiredProcess: selectedIncoterm?.requirePortShippingProcess,
                            },
                            watchedFields: {
                                shipmentLoadingPort: true,
                                shipmentDestinationPort: true,
                                shipmentPlaceAtDestination: true,
                            },
                        }}
                        fields={{
                            shipmentDischargePort: {
                                required: false,
                                disabled: disabled,
                                code: {
                                    name: 'freightDischargePort',
                                    onSelect: onSelectDischargePort,
                                },
                                name: {
                                    labelText: t('Discharge Port'),
                                    name: 'freightDischargePortName',
                                    disabled: true,
                                },
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'freight',
                            },
                            shipmentDischargeCountry: {
                                required: false,
                                disabled: true,
                                code: {
                                    name: 'freightDischargeCountry',
                                },
                                name: {
                                    name: 'freightDischargeCountryName',
                                },
                            },
                            shipmentIntransitTo: {
                                disabled: disabled,
                                code: {
                                    name: 'freightIntransitTo',
                                    onSelect: onSelectIntransitTo,
                                },
                                name: {
                                    name: 'freightIntransitToName',
                                    disabled: true,
                                },
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'freight',
                            },
                            shipmentDestinationPort: {
                                required: required,
                                disabled: disabled,
                                code: {
                                    name: 'freightDestinationPort',
                                    onSelect: onSelectDestinationPort,
                                    disabled: disabled,
                                },
                                name: {
                                    labelText: t('Destination Port Name'),
                                    name: 'freightDestinationPortName',
                                    disabled: true,
                                },
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'freight',
                            },
                            shipmentDestinationCountry: {
                                required: false,
                                disabled: true,
                                code: {
                                    name: 'freightDestinationCountry',
                                },
                                name: {
                                    name: 'freightDestinationCountryName',
                                },
                            },
                            shipmentPlaceAtDestination: {
                                name: 'freightPlaceAtDestination',
                                required: required ? requirePlaceAtDestination : false,
                                disabled: disabled,
                                onBlur: onBlurPlaceAtDestination,
                            },
                        }}
                    />
                </RowLayout.ThreeCol>

                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Dimension (cm.)')}`}>
                            <Form.Item
                                noStyle
                                id="freightDimension"
                                name="freightDimension"
                                rules={[
                                    () => ({
                                        validator: (_, val) => {
                                            return validateMaxByte(val, 30, t('Dimension (cm.)'));
                                        },
                                    }),
                                ]}
                            >
                                <Input placeholder={t('Width* Length* Height')}></Input>
                            </Form.Item>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Cubic Meters (CBM)')}`} name="CBM" required={required && isRequireCBM}>
                            <Row gutter={8}>
                                <Col span={12}>
                                    <Form.Item
                                        noStyle
                                        id="freightCubicMeters"
                                        name="freightCubicMeters"
                                        rules={[
                                            {
                                                required: required && isRequireCBM,
                                                message: `${t('Cubic Meters (CBM)')} ${t('is required')}`,
                                            },
                                            {
                                                validator: (_, value) => {
                                                    if (required && isRequireCBM && value?.length === 0) {
                                                        return Promise.resolve();
                                                    }
                                                    if (required && isRequireCBM && Number(value) <= 0) {
                                                        return Promise.reject(
                                                            new Error(`${t('Cubic Meters (CBM)')} ${t('should be greater than 0')}`),
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <Numeric
                                            id="freightCubicMeters"
                                            onChange={() => {}}
                                            decimalScale={2}
                                            disable={disabled}
                                            isAllowed={(inputObj: any) => {
                                                const { value } = inputObj;
                                                return value >= 0 && value <= 99999999.99;
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item noStyle id="freightCubicUnit" name="freightCubicUnit">
                                        <Input disabled></Input>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Form.Item label={`${t('No. and Kind of Packages')}`} required={required}>
                            <Row gutter={8}>
                                <Col span={12}>
                                    <Form.Item
                                        noStyle
                                        id="freightNoOfPackage"
                                        name="freightNoOfPackage"
                                        rules={[
                                            { required: required, message: `${t('No. and Kind of Packages')} ${t('is required')}` },
                                            {
                                                validator: (_, value) => {
                                                    if (required && value?.length === 0) {
                                                        return Promise.resolve();
                                                    }
                                                    if (required && Number(value) <= 0) {
                                                        return Promise.reject(
                                                            new Error(`${t('No. and Kind of Packages')} ${t('should be greater than 0')}`),
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <Numeric
                                            id="freightNoOfPackage"
                                            onChange={() => {}}
                                            decimalScale={5}
                                            disable={disabled}
                                            isAllowed={(inputObj: any) => {
                                                const { value } = inputObj;
                                                return value >= 0 && value <= 9999999;
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <FormSelectUnit
                                        label={'Unit'}
                                        id="freightNoOfPackageUnit"
                                        name="freightNoOfPackageUnit"
                                        required={required}
                                        disabled={disabled}
                                        onSelect={onSelectNoOfPackageUnit}
                                        apiService={SERVICE.FREIGHT}
                                        prefixID="freight"
                                        formItemProps={{
                                            noStyle: true,
                                        }}
                                    />
                                </Col>
                            </Row>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Gross Weight')}`} required={required}>
                            <Row gutter={8}>
                                <Col span={12}>
                                    <Form.Item
                                        noStyle
                                        id="freightGrossWeight"
                                        name="freightGrossWeight"
                                        rules={[
                                            { required: required, message: `${t('Gross Weight')} ${t('is required')}` },
                                            {
                                                validator: (_, value) => {
                                                    if (required && value?.length === 0) {
                                                        return Promise.resolve();
                                                    }
                                                    if (required && Number(value) <= 0) {
                                                        return Promise.reject(
                                                            new Error(`${t('Gross Weight')} ${t('should be greater than 0')}`),
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <Numeric
                                            id="freightGrossWeight"
                                            onChange={() => {}}
                                            decimalScale={5}
                                            disable={disabled}
                                            isAllowed={(inputObj: any) => {
                                                const { value } = inputObj;
                                                return value >= 0 && value <= 9999999;
                                            }}
                                            onBlur={onBlurGrossWeight}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item noStyle id="freightGrossWeightUnit" name="freightGrossWeightUnit">
                                        <Input disabled></Input>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.TwoThirds>
                        <FormSelectPaymentTerm
                            disabled={disabled}
                            label={`${t('Payment Term')}`}
                            id={`freightPaymentTerm`}
                            name={`freightPaymentTerm`}
                            onSelect={onSelectPaymentTerm}
                            modalProps={{
                                title: `${t('Payment Term')}`,
                            }}
                            apiService={SERVICE.FREIGHT}
                            prefixID="freight"
                        />
                    </ColLayout.TwoThirds>
                    <SharedPlanLoadShipmentIncotermDetail<ITFFreightBookingRequisitionForm>
                        concatPortConfig={{
                            watchedFields: {
                                shipmentIncotermSearch: true,
                            },
                            processData: {
                                inputNameLoadingPortName: 'freightLoadingPortName',
                                inputNameDestinationPortName: 'freightDestinationPortName',
                                inputNamePlaceAtDestination: 'freightPlaceAtDestination',
                                stuffingPlaceName: dataSourceStuffingPlace[0]?.stuffingPlaceName,
                            },
                        }}
                        fields={{
                            shipmentIncotermSearch: {
                                name: 'freightIncoterm',
                                required: required,
                                disabled: disabled,
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'freight',
                                onSelect: onSelectIncoterm,
                            },
                            shipmentIncotermPort: {
                                name: 'freightIncotermPort',
                                required: false,
                            },
                        }}
                    />
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item label={`${t('Freight Mode')}`} id="freightMode" name="freightMode">
                                    <Select allowClear>
                                        {FREIGHT_MODE?.map((item: any) => {
                                            return (
                                                <Option value={item} key={item}>
                                                    {item}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label={`${t('Freight Term')}`} id="freightTerm" name="freightTerm">
                                    <Select allowClear>
                                        {FREIGHT_TERM?.map((item: any) => {
                                            return (
                                                <Option value={item} key={item}>
                                                    {item}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item
                            label={`${t('Freight Charge')}`}
                            name="freightCharge"
                            rules={[
                                {
                                    required: required,
                                    message: `${t('Freight Charge')} ${t('is required')}`,
                                },
                            ]}
                        >
                            <Radio.Group className="custom-space-radio">
                                <Radio id="freightChargeCollect" value={FREIGHT_CHARGE.COLLECT}>
                                    {FREIGHT_CHARGE.COLLECT}
                                </Radio>
                                <Radio id="freightChargePrepaid" value={FREIGHT_CHARGE.PREPAID}>
                                    {FREIGHT_CHARGE.PREPAID}
                                </Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item
                            label={`${t('Handling Charge')}`}
                            name="freightHandlingCharge"
                            rules={[
                                {
                                    required: required,
                                    message: `${t('Handling Charge')} ${t('is required')}`,
                                },
                            ]}
                        >
                            <Radio.Group className="custom-space-radio">
                                <Radio id="freightHandlingChargeCollect" value={HANDLING_CHARGE.COLLECT}>
                                    {HANDLING_CHARGE.COLLECT}
                                </Radio>
                                <Radio id="freightHandlingChargePrepaid" value={HANDLING_CHARGE.PREPAID}>
                                    {HANDLING_CHARGE.PREPAID}
                                </Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>

                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item label={`${t('CY Stuffing Date')}`} id="freightCyStuffingDate" name="freightCyStuffingDate">
                                    <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    label={`${t('Stuffing Date')}`}
                                    id="freightStuffingDate"
                                    name="freightStuffingDate"
                                    rules={[
                                        {
                                            required: false,
                                            message: `${t('Stuffing Date')} ${t('is required')}`,
                                        },
                                    ]}
                                >
                                    <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item label={`${t('Return Load Date')}`} id="freightReturnLoadDate" name="freightReturnLoadDate">
                                    <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item
                                    label={`${t('ETD Date')}`}
                                    id="freightEtdDate"
                                    name="freightEtdDate"
                                    rules={[
                                        {
                                            required: required,
                                            message: `${t('ETD Date')} ${t('is required')}`,
                                        },
                                    ]}
                                >
                                    <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label={`${t('ETA Date')}`} id="freightEtaDate" name="freightEtaDate">
                                    <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" disabled />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
            </Container>

            {loadingType === LOADING_TYPE.CY ? (
                <Row style={{ marginTop: '36px' }}>
                    <Col span={24}>
                        <ShipmentStuffingPlace<ITFTableFreightShipmentStuffingPlace, ITFFormFreightBookingReq>
                            form={form}
                            name="stuffingPlaces"
                            data={dataSourceStuffingPlace}
                            latestKey={latestKey}
                            disabled={disabled}
                            required={required && requireStuffingPlace}
                            action={action}
                            onSelect={onSelectStuffingPlace}
                            onOkDelete={onOkDeleteStuffingPlace}
                            onIncreaseLatestKey={onIncreaseLatestKey}
                            btnAddText={t('Add Master Data')}
                            callbackApi={fetchDataFreightStuffingPlace}
                            defaultLimitStuffingPlace={defaultLimitStuffingPlace}
                            transformStuffingPlace={transformStuffingPlace}
                        />
                    </Col>
                </Row>
            ) : (
                <></>
            )}
            <Container
                style={{
                    marginTop: '36px',
                }}
            >
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Tax Type')}`} name="freightTaxType" id="freightTaxType">
                            <Input disabled></Input>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item
                            label={`${t('Gen Set')}`}
                            name="freightGenSet"
                            rules={[
                                {
                                    required: required,
                                    message: `${t('Gen Set')} ${t('is required')}`,
                                },
                            ]}
                        >
                            <Radio.Group className="custom-space-radio">
                                <Radio id="freight_shipDetailradioCFS" value={YES_NO.YES}>{`${t(YES_NO.YES)}`}</Radio>
                                <Radio id="freight_shipDetailradioCY" value={YES_NO.NO}>{`${t(YES_NO.NO)}`}</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <FormRadioLoadingType
                            label={`${t('Loading Type')}`}
                            name="freightLoadingType"
                            rules={[
                                {
                                    required: required,
                                    message: `${t('Loading Type')} ${t('is required')}`,
                                },
                            ]}
                            disabled={disabled}
                            onChange={onChangeLoadingType}
                        />
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <FormSelectTransportationAgent
                            label={t('Transportation Agent')}
                            id="freightTransportationAgent"
                            name="freightTransportationAgent"
                            disabled={disabled}
                            onSelect={onSelectTransportationAgent}
                            apiService={SERVICE.FREIGHT}
                            prefixID="freight"
                        />
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item
                            label={`${t('Factory Contact')}`}
                            name="freightFactortContact"
                            id="freightFactortContact"
                            rules={[
                                () => ({
                                    validator: (_, val) => validateMaxByte(val, 30, t('Factory Contact')),
                                }),
                            ]}
                        >
                            <Input></Input>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item
                            label={`${t('Axle Type')}`}
                            name="freightAxleType"
                            rules={[
                                {
                                    required: required,
                                    message: `${t('Axle Type')} ${t('is required')}`,
                                },
                            ]}
                        >
                            <Radio.Group className="group-radio">
                                <Radio id="freight_shipDetailradioCFS" value={AXLE_TYPE.NA}>{`${t(AXLE_TYPE.NA)}`}</Radio>
                                <Radio id="freight_shipDetailradioCY" value={AXLE_TYPE.TWO}>{`${t(AXLE_TYPE.TWO)}`}</Radio>
                                <Radio id="freight_shipDetailradioCY" value={AXLE_TYPE.THREE}>{`${t(AXLE_TYPE.THREE)}`}</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <FormSelectVendorGeneral
                            label={t('CY Billing To')}
                            id="freightCyBillingTo"
                            name="freightCyBillingTo"
                            disabled={disabled}
                            onSelect={onSelectCyBilling}
                            apiService={SERVICE.FREIGHT}
                            prefixID="freight"
                        />
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Half style={{ marginBottom: 20 }}>
                        <CustomFormItemTextArea
                            label={t('CY Remark')}
                            id="freightCyRemark"
                            name="freightCyRemark"
                            disabled={disabled}
                            maxByte={500}
                        />
                    </ColLayout.Half>
                    <ColLayout.Half style={{ marginBottom: 20 }}>
                        <CustomFormItemTextArea
                            label={t('Requisition Remark')}
                            id="freightRequisitionRemark"
                            name="freightRequisitionRemark"
                            disabled={disabled}
                            maxByte={200}
                        />
                    </ColLayout.Half>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Half>
                        <Form.Item
                            label={`${t('Additional Condition (from Plan Load)')}`}
                            name="freightAdditionalConditionFromPlanload"
                            id="freightAdditionalConditionFromPlanload"
                            rules={[
                                () => ({
                                    validator: (_, val) => validateMaxByte(val, 3000, t('Additional Condition (from Plan Load)')),
                                }),
                            ]}
                        >
                            <TextArea disabled={true} />
                        </Form.Item>
                    </ColLayout.Half>
                    <ColLayout.Half>
                        <Form.Item
                            label={`${t('Shipment Remark')}`}
                            name="freightShipmentRemark"
                            id="freightShipmentRemark"
                            rules={[
                                () => ({
                                    validator: (_, val) => validateMaxByte(val, 3000, t('Shipment Remark')),
                                }),
                            ]}
                        >
                            <TextArea disabled={true} />
                        </Form.Item>
                    </ColLayout.Half>
                </RowLayout.ThreeCol>
            </Container>
        </>
    );
};
export default FreightBookingRequisitionShipmentDetail;
