import { useTranslation } from 'react-i18next';

import { Col, Form, Input, Row, TimePicker } from 'antd';

import CustomDatePicker from 'common/CustomDatePicker';
import FormSelectAgentSplitFields from 'common/FormSelect/FormSelectAgentSplitFields';
import FormSelectUnit from 'common/FormSelect/FormSelectUnit';
import ColLayout from 'common/InitiativeDetail/ColLayout';
import RowLayout from 'common/InitiativeDetail/RowLayout';
import { Numeric } from 'common/InputNumber';
import { timeFormat } from 'components/Export/PlanLoad/BodyPlanLoad/PlanLoadFreightInformation/PlanLoadFreightInformation';

import { SERVICE } from 'constants/service.constant';
import type { ITFFormAirFreightBookingAirFreightBooking } from 'types/Export/AirFreightBooking/Form/FormAirFreightBookingAirFreightBooking.type';

import { localeDatepicker } from 'helper/FormatDate';
import {
    isAfterEqDate,
    isAfterEqDateTime,
    isBeforeEqDate,
    isBeforeEqDateTime,
    validateMaxByte,
    validateMaxValueByDigitNumber,
} from 'helper/Validators';
import { useAirFreightBookingAirFreightBooking } from 'hooks/export/air-freight-booking/useAirFreightBookingAirFreightBooking';

import { Container, ContainerChild } from './AirFreightBookingAirFreightBooking.styles';
import JobBooking from './AirFreightBookingJobBooking/AirFreightBookingJobBooking';

import { useAirFreightBookingFieldControlContext } from 'contexts/Export/AirFreightBooking/AirFreightBookingFieldControlContext';
import type dayjs from 'dayjs';
import SharedPlanLoadShipmentShipByDetail from 'shared/Export/SharedPlanLoad/SharedPlanLoadShipmentShipByDetail';

export const dateFormat = 'DD/MM/YYYY';

const AirFreightBookingAirFreightBooking = () => {
    const { t } = useTranslation();
    const { airFreightBookingAirFreightBookingDisabled: disabled, airFreightBookingAirFreightBookingRequired: required } =
        useAirFreightBookingFieldControlContext();
    const {
        etaDate,
        etdDate,
        disableETADate,
        disableFieldETADate,
        setSelectETATime,
        isSameETDate,
        etaTime,
        etdTime,
        validateETDAndETADateTime,
        disableEndTime,
        onSelectAirFreightBookingAgent,
        onChangeAirFreightBookingETDDate,
    } = useAirFreightBookingAirFreightBooking();

    return (
        <Container>
            <ContainerChild>
                <RowLayout.ThreeCol>
                    <SharedPlanLoadShipmentShipByDetail<ITFFormAirFreightBookingAirFreightBooking>
                        fields={{
                            shipmentCommodity: {
                                label: t('Commodity'),
                                name: 'airFreightBookingAirFreightBookingCommodity',
                                valueFieldName: 'airFreightBookingAirFreightBookingCommodityCode',
                                labelFieldName: 'airFreightBookingAirFreightBookingCommodityName',
                                required: required.airFreightBookingAirFreightBookingCommodity,
                                disabled: disabled.airFreightBookingAirFreightBookingCommodity,
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'airFreightBooking',
                            },
                        }}
                    />
                    <ColLayout.TwoThirds>
                        <FormSelectAgentSplitFields<ITFFormAirFreightBookingAirFreightBooking>
                            required={required.airFreightBookingAirFreightBookingAgent}
                            label={`${t('Agent')}`}
                            disabled={disabled.airFreightBookingAirFreightBookingAgent}
                            nameSelect="airFreightBookingAirFreightBookingAgent"
                            apiService={SERVICE.FREIGHT}
                            prefixID="carrierAssignment"
                            nameInputText="airFreightBookingAirFreightBookingAgentName"
                            disabledInputText={disabled.airFreightBookingAirFreightBookingAgentName}
                            colProps={{
                                select: {
                                    span: 8,
                                    xl: 6,
                                },
                                inputText: {
                                    span: 16,
                                    xl: 18,
                                },
                            }}
                            onSelect={onSelectAirFreightBookingAgent}
                        />
                    </ColLayout.TwoThirds>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                                    label={t('ETD Date (FR)')}
                                    name="airFreightBookingAirFreightBookingEtdDate"
                                    rules={[
                                        {
                                            required: required.airFreightBookingAirFreightBookingEtdDate,
                                            message: `${t('ETD Date')} ${t('is required')}`,
                                        },
                                        {
                                            validator: (_, val) => isBeforeEqDate(val, etaDate, 'ETD Date', 'ETA Date'),
                                        },
                                    ]}
                                >
                                    <CustomDatePicker
                                        placeholder={t('Select Date')}
                                        format={dateFormat}
                                        locale={localeDatepicker()}
                                        style={{ width: '100%' }}
                                        disabled={disabled.airFreightBookingAirFreightBookingEtdDate}
                                        onChange={onChangeAirFreightBookingETDDate}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                                    label={t('ETD Time (FR)')}
                                    name="airFreightBookingAirFreightBookingEtdTime"
                                    rules={[
                                        {
                                            required: required.airFreightBookingAirFreightBookingEtdTime,
                                            message: `${t('ETD Time')} ${t('is required')}`,
                                        },
                                        {
                                            validator: isSameETDate
                                                ? (_, val) => isBeforeEqDateTime(val, etaTime, 'ETD Time', 'ETA Time')
                                                : () => Promise.resolve(),
                                        },
                                    ]}
                                >
                                    <TimePicker
                                        placeholder={t('Select Time')}
                                        style={{ width: '100%' }}
                                        format={timeFormat}
                                        locale={localeDatepicker()}
                                        disabled={disabled.airFreightBookingAirFreightBookingEtdTime}
                                        onChange={validateETDAndETADateTime}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                                    label={t('ETA Date (FR)')}
                                    name="airFreightBookingAirFreightBookingEtaDate"
                                    rules={[
                                        {
                                            required: required.airFreightBookingAirFreightBookingEtaDate,
                                            message: `${t('ETD Date')} ${t('is required')}`,
                                        },
                                        {
                                            validator: (_, val) => isAfterEqDate(val, etdDate, 'ETA Date', 'ETD Date'),
                                        },
                                    ]}
                                >
                                    <CustomDatePicker
                                        placeholder={t('Select Date')}
                                        format={dateFormat}
                                        locale={localeDatepicker()}
                                        style={{ width: '100%' }}
                                        disabled={disabled.airFreightBookingAirFreightBookingEtaDate || disableFieldETADate}
                                        disabledDate={disableETADate}
                                        onChange={validateETDAndETADateTime}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                                    label={t('ETA Time (FR)')}
                                    name="airFreightBookingAirFreightBookingEtaTime"
                                    rules={[
                                        {
                                            required: required.airFreightBookingAirFreightBookingEtaTime,
                                            message: `${t('ETA Time')} ${t('is required')}`,
                                        },
                                        {
                                            validator: isSameETDate
                                                ? (_, val) => isAfterEqDateTime(val, etdTime, 'ETA Time', 'ETD Time')
                                                : () => Promise.resolve(),
                                        },
                                    ]}
                                >
                                    <TimePicker
                                        placeholder={t('Select Time')}
                                        style={{ width: '100%' }}
                                        format={timeFormat}
                                        locale={localeDatepicker()}
                                        disabled={disabled.airFreightBookingAirFreightBookingEtaTime || disableFieldETADate}
                                        disabledTime={disableEndTime}
                                        onCalendarChange={(time: dayjs.Dayjs | dayjs.Dayjs[]) => setSelectETATime(time as dayjs.Dayjs)}
                                        onChange={validateETDAndETADateTime}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                                    label={t('Due Date')}
                                    name="airFreightBookingAirFreightBookingDueDate"
                                    rules={[
                                        {
                                            required: required.airFreightBookingAirFreightBookingDueDate,
                                            message: `${t('Due Date')} ${t('is required')}`,
                                        },
                                    ]}
                                >
                                    <CustomDatePicker
                                        placeholder={t('Select Date')}
                                        format={dateFormat}
                                        locale={localeDatepicker()}
                                        style={{ width: '100%' }}
                                        disabled={disabled.airFreightBookingAirFreightBookingDueDate}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                                    label={t('Stuffing Date')}
                                    name="airFreightBookingAirFreightBookingStuffingDate"
                                >
                                    <CustomDatePicker
                                        placeholder=""
                                        format={dateFormat}
                                        locale={localeDatepicker()}
                                        style={{ width: '100%' }}
                                        disabled={disabled.airFreightBookingAirFreightBookingStuffingDate}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                            label={t('AWB No.')}
                            name="airFreightBookingAirFreightBookingAwbNo"
                            rules={[
                                {
                                    whitespace: true,
                                    required: required.airFreightBookingAirFreightBookingAwbNo,
                                    message: `${t('AWB No.')} ${t('is required')}`,
                                },
                                () => ({
                                    validator: (_, val) => {
                                        return validateMaxByte(val, 20, t('AWB No.'));
                                    },
                                }),
                            ]}
                        >
                            <Input disabled={disabled.airFreightBookingAirFreightBookingAwbNo} />
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                            label={t('HAWB No.')}
                            name="airFreightBookingAirFreightBookingHawbNo"
                            rules={[
                                () => ({
                                    validator: (_, val) => {
                                        return validateMaxByte(val, 20, t('HWB No.'));
                                    },
                                }),
                            ]}
                        >
                            <Input disabled={disabled.airFreightBookingAirFreightBookingHawbNo} />
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                            label={t('Booking Ref.')}
                            name="airFreightBookingAirFreightBookingBookingRefNo"
                            rules={[
                                () => ({
                                    validator: (_, val) => {
                                        return validateMaxByte(val, 60, t('Booking Ref.'));
                                    },
                                }),
                            ]}
                        >
                            <Input disabled={disabled.airFreightBookingAirFreightBookingBookingRefNo} />
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                                    label={t('AWB Date')}
                                    name="airFreightBookingAirFreightBookingAwbDate"
                                    rules={[
                                        {
                                            required: required.airFreightBookingAirFreightBookingAwbDate,
                                            message: `${t('AWB Date')} ${t('is required')}`,
                                        },
                                    ]}
                                >
                                    <CustomDatePicker
                                        placeholder=""
                                        format={dateFormat}
                                        locale={localeDatepicker()}
                                        style={{ width: '100%' }}
                                        disabled={disabled.airFreightBookingAirFreightBookingAwbDate}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.TwoThirds>
                        <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                            label={t('Connecting At')}
                            name="airFreightBookingAirFreightBookingConnectingAt"
                            rules={[
                                () => ({
                                    validator: (_, val) => {
                                        return validateMaxByte(val, 50, t('Connecting At'));
                                    },
                                }),
                            ]}
                        >
                            <Input disabled={disabled.airFreightBookingAirFreightBookingConnectingAt} />
                        </Form.Item>
                    </ColLayout.TwoThirds>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item
                                    label={t('Charge Weight')}
                                    name="airFreightBookingAirFreightBookingChargeWeight"
                                    rules={[
                                        {
                                            required: required.airFreightBookingAirFreightBookingChargeWeight,
                                            message: `${t('Charge Weight')} ${t('is required')}`,
                                        },
                                        {
                                            validator: (_, val) => {
                                                return validateMaxValueByDigitNumber(val, 7, 'Charge Weight');
                                            },
                                        },
                                    ]}
                                >
                                    <Numeric decimalScale={5} disable={disabled.airFreightBookingAirFreightBookingChargeWeight} />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <FormSelectUnit
                                    label={''}
                                    name="airFreightBookingAirFreightBookingChargeWeightUnit"
                                    required={required.airFreightBookingAirFreightBookingChargeWeightUnit}
                                    disabled={disabled.airFreightBookingAirFreightBookingChargeWeightUnit}
                                    apiService={SERVICE.FREIGHT}
                                    prefixID="airFreightBooking"
                                    formItemProps={{
                                        style: { marginTop: 31, marginBottom: 0 },
                                    }}
                                    modalProps={{
                                        title: t('Unit'),
                                    }}
                                />
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item
                                    label={t('Dry Ice')}
                                    name="airFreightBookingAirFreightBookingDryIce"
                                    rules={[
                                        {
                                            validator: (_, val) => {
                                                return validateMaxValueByDigitNumber(val, 7, 'Dry Ice');
                                            },
                                        },
                                    ]}
                                >
                                    <Numeric
                                        decimalScale={5}
                                        placeholder="0.00000"
                                        disable={disabled.airFreightBookingAirFreightBookingDryIce}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <FormSelectUnit
                                    label={''}
                                    name="airFreightBookingAirFreightBookingDryIceUnit"
                                    required={required.airFreightBookingAirFreightBookingDryIceUnit}
                                    disabled={disabled.airFreightBookingAirFreightBookingDryIceUnit}
                                    apiService={SERVICE.FREIGHT}
                                    prefixID="airFreightBooking"
                                    formItemProps={{
                                        style: { marginTop: 31, marginBottom: 0 },
                                    }}
                                    modalProps={{
                                        title: t('Unit'),
                                    }}
                                    placeholder="KG"
                                />
                            </Col>
                        </Row>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.TwoCol>
                    <ColLayout.TwoThirds>
                        <Form.Item<ITFFormAirFreightBookingAirFreightBooking>
                            label={t('Tax Type')}
                            name="airFreightBookingAirFreightBookingTaxType"
                        >
                            <Input disabled={disabled.airFreightBookingAirFreightBookingTaxType} />
                        </Form.Item>
                    </ColLayout.TwoThirds>
                </RowLayout.TwoCol>
            </ContainerChild>
            <ContainerChild>
                <JobBooking />
            </ContainerChild>
        </Container>
    );
};
export default AirFreightBookingAirFreightBooking;
