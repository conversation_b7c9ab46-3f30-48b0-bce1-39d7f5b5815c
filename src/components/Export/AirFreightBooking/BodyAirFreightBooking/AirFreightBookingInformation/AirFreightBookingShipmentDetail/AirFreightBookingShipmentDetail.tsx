import { useTranslation } from 'react-i18next';

import { Col, Form, Input, Radio, Row } from 'antd';

import CustomDatePicker from 'common/CustomDatePicker';
import FormRadioLoadingType from 'common/FormRadio/FormRadioLoadingType';
import FormSelectUnit from 'common/FormSelect/FormSelectUnit';
import ColLayout from 'common/InitiativeDetail/ColLayout';
import RowLayout from 'common/InitiativeDetail/RowLayout';
import { Numeric } from 'common/InputNumber';
import { HeaderSemiBold18 } from 'common/Text.styles';

import { SERVICE } from 'constants/service.constant';
import { HANDLING_CHARGE, PRODUCT_TYPE } from 'constants/shipment.constant';
import type { ITFFormAirFreightBookingInformation } from 'types/Export/AirFreightBooking/Form/FormAirFreightBookingInformation.type';

import { Container } from './AirFreightBookingShipmentDetail.styles';

import SharedPlanLoadShipmentShipByDetail from 'shared/Export/SharedPlanLoad/SharedPlanLoadShipmentShipByDetail';
import SharedPlanLoadShipmentShippingDetail from 'shared/Export/SharedPlanLoad/SharedPlanLoadShipmentShippingDetail';

const AirFreightBookingShipmentDetail = () => {
    const { t } = useTranslation();

    return (
        <Container>
            <HeaderSemiBold18 style={{ marginBottom: '20px' }}>{`${t('Shipment Detail')}`}</HeaderSemiBold18>
            <RowLayout.ThreeCol>
                <SharedPlanLoadShipmentShipByDetail<ITFFormAirFreightBookingInformation>
                    fields={{
                        shipmentCommodity: {
                            label: t('Trade Commodity'),
                            name: 'airFreightBookingShipmentTradeCommodity',
                            valueFieldName: 'airFreightBookingShipmentTradeCommodity',
                            required: false,
                            disabled: true,
                            apiService: SERVICE.FREIGHT,
                            prefixID: 'airFreightBooking',
                        },
                        shipmentShipBy: {
                            name: 'airFreightBookingShipmentShipBy',
                            disabled: true,
                            required: false,
                        },
                    }}
                />
                <ColLayout.Third>
                    <Form.Item<ITFFormAirFreightBookingInformation>
                        label={`${t('Product Type')}`}
                        name="airFreightBookingShipmentProductType"
                    >
                        <Radio.Group className="custom-space-radio" disabled>
                            <Radio id="airFreightBookingShipmentProductTypeSimple" value={PRODUCT_TYPE.SAMPLE}>{`${t(
                                PRODUCT_TYPE.SAMPLE,
                            )}`}</Radio>
                            <Radio id="airFreightBookingShipmentProductTypeNormal" value={PRODUCT_TYPE.NORMAL}>{`${t(
                                PRODUCT_TYPE.NORMAL,
                            )}`}</Radio>
                        </Radio.Group>
                    </Form.Item>
                </ColLayout.Third>
            </RowLayout.ThreeCol>
            <RowLayout.ThreeCol>
                <SharedPlanLoadShipmentShippingDetail<ITFFormAirFreightBookingInformation>
                    fields={{
                        shipmentLoadingPort: {
                            required: false,
                            disabled: true,
                            code: {
                                name: 'airFreightBookingShipmentLoadingPortCode',
                            },
                            name: {
                                labelText: t('Loading Port Name'),
                                name: 'airFreightBookingShipmentLoadingPortName',
                                disabled: true,
                            },
                            apiService: SERVICE.FREIGHT,
                            prefixID: 'airFreightBooking',
                        },
                        shipmentLoadingCountry: {
                            required: false,
                            disabled: true,
                            code: {
                                name: 'airFreightBookingShipmentLoadingCountryCode',
                            },
                            name: {
                                name: 'airFreightBookingShipmentLoadingCountryName',
                            },
                        },
                    }}
                />
            </RowLayout.ThreeCol>
            <RowLayout.ThreeCol>
                <SharedPlanLoadShipmentShippingDetail<ITFFormAirFreightBookingInformation>
                    fields={{
                        shipmentDestinationPort: {
                            required: false,
                            disabled: true,
                            code: {
                                name: 'airFreightBookingShipmentDestinationPortCode',
                                disabled: true,
                            },
                            name: {
                                labelText: t('Destination Port Name'),
                                name: 'airFreightBookingShipmentDestinationPortName',
                                disabled: true,
                            },
                            apiService: SERVICE.FREIGHT,
                            prefixID: 'airFreightBooking',
                        },
                        shipmentDestinationCountry: {
                            required: false,
                            disabled: true,
                            code: {
                                name: 'airFreightBookingShipmentDestinationCountryCode',
                            },
                            name: {
                                name: 'airFreightBookingShipmentDestinationCountryName',
                            },
                        },
                    }}
                />
                <ColLayout.Third>
                    <FormRadioLoadingType<ITFFormAirFreightBookingInformation>
                        classNameRadio="custom-space-radio"
                        label={`${t('Loading Type')}`}
                        name="airFreightBookingShipmentLoadingType"
                        disabled={true}
                    />
                </ColLayout.Third>
            </RowLayout.ThreeCol>
            <RowLayout.ThreeCol>
                <ColLayout.Third>
                    <Form.Item<ITFFormAirFreightBookingInformation> label={`${t('Dimension (cm.)')}`}>
                        <Form.Item noStyle name="airFreightBookingShipmentDimension">
                            <Input placeholder={t('Width* Length* Height')} disabled></Input>
                        </Form.Item>
                    </Form.Item>
                </ColLayout.Third>
                <ColLayout.Third>
                    <Form.Item label={`${t('Cubic Meters')}`} name="CBM" required={false}>
                        <Row gutter={[8, 8]}>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingInformation> noStyle name="airFreightBookingShipmentCubicMeters">
                                    <Numeric onChange={() => {}} decimalScale={2} disable={true} />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingInformation> noStyle name="airFreightBookingShipmentCubicMetersUnit">
                                    <Input disabled></Input>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form.Item>
                </ColLayout.Third>
                <ColLayout.Third>
                    <Form.Item label={`${t('Handling Charge')}`} name="airFreightBookingShipmentHandlingCharge">
                        <Radio.Group className="custom-space-radio" disabled>
                            <Radio value={HANDLING_CHARGE.COLLECT}>{HANDLING_CHARGE.COLLECT}</Radio>
                            <Radio value={HANDLING_CHARGE.PREPAID}>{HANDLING_CHARGE.PREPAID}</Radio>
                        </Radio.Group>
                    </Form.Item>
                </ColLayout.Third>
            </RowLayout.ThreeCol>
            <RowLayout.ThreeCol>
                <ColLayout.Third>
                    <Form.Item label={`${t('No. and Kind of Packages')}`} required={false}>
                        <Row gutter={[8, 8]}>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingInformation> noStyle name="airFreightBookingShipmentNoAndKindOfPackages">
                                    <Numeric onChange={() => {}} decimalScale={5} disable={true} />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <FormSelectUnit
                                    label={'Unit'}
                                    name="airFreightBookingShipmentNoAndKindOfPackagesUnit"
                                    required={false}
                                    disabled={true}
                                    apiService={SERVICE.FREIGHT}
                                    prefixID="airFreightBooking"
                                    formItemProps={{
                                        noStyle: true,
                                    }}
                                />
                            </Col>
                        </Row>
                    </Form.Item>
                </ColLayout.Third>
                <ColLayout.Third>
                    <Form.Item<ITFFormAirFreightBookingInformation> label={`${t('Gross Weight')}`} required={false}>
                        <Row gutter={[8, 8]}>
                            <Col span={12}>
                                <Form.Item noStyle name="airFreightBookingShipmentGrossWeight">
                                    <Numeric onChange={() => {}} decimalScale={5} disable={true} />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item<ITFFormAirFreightBookingInformation> noStyle name="airFreightBookingShipmentGrossWeightUnit">
                                    <Input disabled></Input>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form.Item>
                </ColLayout.Third>
                <ColLayout.Third>
                    <Row gutter={[8, 8]}>
                        <Col span={12}>
                            <Form.Item<ITFFormAirFreightBookingInformation>
                                label={`${t('ETD Date')}`}
                                name="airFreightBookingShipmentETDDate"
                            >
                                <CustomDatePicker format="DD/MM/YYYY" style={{ width: '100%' }} placeholder="" disabled />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item<ITFFormAirFreightBookingInformation>
                                label={`${t('ETA Date')}`}
                                name="airFreightBookingShipmentETADate"
                            >
                                <CustomDatePicker format="DD/MM/YYYY" style={{ width: '100%' }} placeholder="" disabled />
                            </Form.Item>
                        </Col>
                    </Row>
                </ColLayout.Third>
            </RowLayout.ThreeCol>
        </Container>
    );
};
export default AirFreightBookingShipmentDetail;
