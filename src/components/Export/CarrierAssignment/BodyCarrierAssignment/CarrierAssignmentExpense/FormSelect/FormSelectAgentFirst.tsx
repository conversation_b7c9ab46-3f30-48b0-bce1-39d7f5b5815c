import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import useColumnCommodity from 'common/FormSelect/Column/useColumnCommodity';
import FormSelectSearchPagination from 'common/FormSelectSearchPagination';

import type { ITFCommodityParams } from 'types/Commodity.type';
import type { ITFFormSelectProps } from 'types/FormSelect/FormSelectProps.type';
import type { TypePagination } from 'types/Pagination.type';

import { useFormSelectAgentFirst } from 'hooks/export/carrier-assignment/useFormSelectAgentFirst';

interface ITFProps<Values = {}> extends ITFFormSelectProps {
    params?: ITFCommodityParams;
    name: keyof Values;
    valueFieldName?: keyof Values;
    labelFieldName?: keyof Values;
}

export const FormSelectAgentFirst = <Values,>(props: ITFProps<Values>) => {
    const { t } = useTranslation();
    const form = Form.useFormInstance();
    const {
        id,
        label,
        name,
        disabled,
        required,
        params,
        onSelect = () => {},
        modalProps,
        prefixID = '',
        mode = 'select',
        placeholder,
    } = props;

    const { columnsCommodity } = useColumnCommodity(prefixID);
    const { fetchDataAgentFirst, transformAgentFirst, transformAgentFirstOptions, onSelectAgentFirst, onClearAgentFirst } =
        useFormSelectAgentFirst({
            ...props,
            form,
        });

    const callbackApi = useCallback(
        (data: TypePagination) => {
            return fetchDataAgentFirst({ ...data, ...params });
        },
        [params],
    );

    return (
        <FormSelectSearchPagination
            {...props}
            label={label}
            id={id}
            name={name}
            mode={mode}
            columns={columnsCommodity}
            disabled={disabled}
            placeholder={placeholder}
            callbackApi={callbackApi}
            transformDataSource={transformAgentFirst}
            transformOptions={transformAgentFirstOptions}
            onSelect={(rec: any) => {
                onSelectAgentFirst(rec);
                onSelect(rec);
            }}
            responseDataKey="data"
            searchable
            onClear={() => {
                onClearAgentFirst();
                onSelect({});
            }}
            rules={[
                {
                    required: required,
                    message: `${label} ${t('is required')}`,
                },
            ]}
            limit={10}
            modalProps={{
                titleId: `${prefixID}LovCommodityTitle`,
                closeIconId: `${prefixID}LovCommodityClose`,
                searchInputId: `${prefixID}LovCommoditySearch`,
                ...modalProps,
            }}
        />
    );
};

export default FormSelectAgentFirst;
