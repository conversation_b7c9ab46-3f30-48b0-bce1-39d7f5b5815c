import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Col, Form, Input, Radio, Row, Select } from 'antd';
import TextArea from 'antd/es/input/TextArea';

import CustomDatePicker from 'common/CustomDatePicker';
import CustomFormItemTextArea from 'common/CustomFormItemTextArea';
import FormRadioLoadingType from 'common/FormRadio/FormRadioLoadingType';
import FormSelectPaymentTerm from 'common/FormSelect/FormSelectPaymentTerm';
import FormSelectTransportationAgent from 'common/FormSelect/FormSelectTransportationAgent';
import FormSelectUnit from 'common/FormSelect/FormSelectUnit';
import FormSelectVendorGeneral from 'common/FormSelect/FormSelectVendorGeneral';
import ColLayout from 'common/InitiativeDetail/ColLayout';
import RowLayout from 'common/InitiativeDetail/RowLayout';
import { Numeric } from 'common/InputNumber';
import { HeaderSemiBold18 } from 'common/Text.styles';

import { SERVICE } from 'constants/service.constant';
import {
    AXLE_TYPE,
    FREIGHT_CHARGE,
    FREIGHT_MODE,
    FREIGHT_TERM,
    HANDLING_CHARGE,
    LOADING_TYPE,
    PRODUCT_TYPE,
    YES_NO,
} from 'constants/shipment.constant';
import type { ITFFormCarrierAssignmentInformation } from 'types/Export/CarrierAssignment/Form/FormCarrierAssignmentInformation.type';

import { validateMaxByte } from 'helper/Validators';
import { carrierAssignmentSelector } from 'store/slice/export/carrier-assignment/carrierAssignment';
import { carrierAssignmentShipmentDetailSelector } from 'store/slice/export/carrier-assignment/carrierAssignmentShipmentDetail';

import CarrierAssignmentStuffingPlace from '../CarrierAssignmentStuffingPlace/CarrierAssignmentStuffingPlace';
import { Container } from './CarrierAssignmentShipmentDetail.style';

import SharedPlanLoadShipmentIncotermDetail from 'shared/Export/SharedPlanLoad/SharedPlanLoadShipmentIncotermDetail';
import SharedPlanLoadShipmentShipByDetail from 'shared/Export/SharedPlanLoad/SharedPlanLoadShipmentShipByDetail';
import SharedPlanLoadShipmentShippingDetail from 'shared/Export/SharedPlanLoad/SharedPlanLoadShipmentShippingDetail';

const { Option } = Select;

const CarrierAssignmentShipmentDetail = () => {
    const { t } = useTranslation();

    const { selectedIncoterm } = useSelector(carrierAssignmentShipmentDetailSelector);
    const { loadingType } = useSelector(carrierAssignmentSelector).shipmentDetail;

    return (
        <>
            <Container>
                <HeaderSemiBold18 style={{ marginTop: '20px', marginBottom: '20px' }}>{`${t('Shipment Detail')}`}</HeaderSemiBold18>
                <RowLayout.ThreeCol>
                    <SharedPlanLoadShipmentShipByDetail<ITFFormCarrierAssignmentInformation>
                        fields={{
                            shipmentCommodity: {
                                label: t('Trade Commodity'),
                                name: 'carrierAssignmentInfoTradeCommodity',
                                valueFieldName: 'carrierAssignmentInfoTradeCommodity',
                                required: false,
                                disabled: true,
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'carrierAssignment',
                            },
                            shipmentShipBy: {
                                name: 'carrierAssignmentInfoShipBy',
                                disabled: true,
                                required: false,
                            },
                        }}
                    />
                    <ColLayout.Third>
                        <Form.Item label={`${t('Product Type')}`} name="carrierAssignmentInfoProductType">
                            <Radio.Group className="custom-space-radio" disabled>
                                <Radio id="carrierAssignmentInfoProductTypeSimple" value={PRODUCT_TYPE.SAMPLE}>{`${t(
                                    PRODUCT_TYPE.SAMPLE,
                                )}`}</Radio>
                                <Radio id="carrierAssignmentInfoProductTypeNormal" value={PRODUCT_TYPE.NORMAL}>{`${t(
                                    PRODUCT_TYPE.NORMAL,
                                )}`}</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <SharedPlanLoadShipmentShippingDetail<ITFFormCarrierAssignmentInformation>
                        concatIncotermPortConfig={{
                            incoterm: {
                                inputNameCode: 'carrierAssignmentInfoIncoterm',
                                inputNamePort: 'carrierAssignmentInfoIncotermPort',
                                requiredProcess: selectedIncoterm?.requirePortShippingProcess,
                            },
                            watchedFields: {
                                shipmentLoadingPort: true,
                                shipmentDestinationPort: true,
                                shipmentPlaceAtDestination: true,
                            },
                        }}
                        fields={{
                            shipmentLoadingPort: {
                                required: false,
                                disabled: true,
                                code: {
                                    name: 'carrierAssignmentInfoLoadingPort',
                                },
                                name: {
                                    labelText: t('Loading Port Name'),
                                    name: 'carrierAssignmentInfoLoadingPortName',
                                    disabled: true,
                                },
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'carrierAssignment',
                            },
                            shipmentLoadingCountry: {
                                required: false,
                                disabled: true,
                                code: {
                                    name: 'carrierAssignmentInfoLoadingCountry',
                                },
                                name: {
                                    name: 'carrierAssignmentInfoLoadingCountryName',
                                },
                            },
                        }}
                    />
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <SharedPlanLoadShipmentShippingDetail<ITFFormCarrierAssignmentInformation>
                        concatIncotermPortConfig={{
                            incoterm: {
                                inputNameCode: 'carrierAssignmentInfoIncoterm',
                                inputNamePort: 'carrierAssignmentInfoIncotermPort',
                                requiredProcess: selectedIncoterm?.requirePortShippingProcess,
                            },
                            watchedFields: {
                                shipmentLoadingPort: true,
                                shipmentDestinationPort: true,
                                shipmentPlaceAtDestination: true,
                            },
                        }}
                        fields={{
                            shipmentDischargePort: {
                                required: false,
                                disabled: true,
                                code: {
                                    name: 'carrierAssignmentInfoDischargePort',
                                },
                                name: {
                                    labelText: t('Discharge Port'),
                                    name: 'carrierAssignmentInfoDischargePortName',
                                    disabled: true,
                                },
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'carrierAssignment',
                            },
                            shipmentDischargeCountry: {
                                required: false,
                                disabled: true,
                                code: {
                                    name: 'carrierAssignmentInfoDischargeCountry',
                                },
                                name: {
                                    name: 'carrierAssignmentInfoDischargeCountryName',
                                },
                            },
                            shipmentIntransitTo: {
                                disabled: true,
                                code: {
                                    name: 'carrierAssignmentInfoIntransitTo',
                                },
                                name: {
                                    name: 'carrierAssignmentInfoIntransitToName',
                                    disabled: true,
                                },
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'carrierAssignment',
                            },
                            shipmentDestinationPort: {
                                required: false,
                                disabled: true,
                                code: {
                                    name: 'carrierAssignmentInfoDestinationPort',
                                    disabled: true,
                                },
                                name: {
                                    labelText: t('Destination Port Name'),
                                    name: 'carrierAssignmentInfoDestinationPortName',
                                    disabled: true,
                                },
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'carrierAssignment',
                            },
                            shipmentDestinationCountry: {
                                required: false,
                                disabled: true,
                                code: {
                                    name: 'carrierAssignmentInfoDestinationCountry',
                                },
                                name: {
                                    name: 'carrierAssignmentInfoDestinationCountryName',
                                },
                            },
                            shipmentPlaceAtDestination: {
                                name: 'carrierAssignmentInfoPlaceAtDestination',
                                required: false,
                                disabled: true,
                            },
                        }}
                    />
                </RowLayout.ThreeCol>

                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Dimension (cm.)')}`}>
                            <Form.Item
                                noStyle
                                name="carrierAssignmentInfoDimension"
                                rules={[
                                    () => ({
                                        validator: (_, val) => {
                                            return validateMaxByte(val, 30, t('Dimension (cm.)'));
                                        },
                                    }),
                                ]}
                            >
                                <Input placeholder={t('Width* Length* Height')} disabled></Input>
                            </Form.Item>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Cubic Meters (CBM)')}`} name="CBM" required={false}>
                            <Row gutter={8}>
                                <Col span={12}>
                                    <Form.Item
                                        noStyle
                                        name="carrierAssignmentInfoCubicMeters"
                                        rules={[
                                            {
                                                required: false,
                                                message: `${t('Cubic Meters (CBM)')} ${t('is required')}`,
                                            },
                                        ]}
                                    >
                                        <Numeric
                                            onChange={() => {}}
                                            decimalScale={2}
                                            disable={true}
                                            isAllowed={(inputObj: any) => {
                                                const { value } = inputObj;
                                                return value >= 0 && value <= 99999999.99;
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item noStyle name="carrierAssignmentInfoCubicMetersUnit">
                                        <Input disabled></Input>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Form.Item label={`${t('No. and Kind of Packages')}`} required={false}>
                            <Row gutter={8}>
                                <Col span={12}>
                                    <Form.Item
                                        noStyle
                                        name="carrierAssignmentInfoNoAndKindOfPackages"
                                        rules={[
                                            { required: false, message: `${t('No. and Kind of Packages')} ${t('is required')}` },
                                            {
                                                validator: (_, value) => {
                                                    if (value?.length === 0) {
                                                        return Promise.resolve();
                                                    }
                                                    if (Number(value) <= 0) {
                                                        return Promise.reject(
                                                            new Error(`${t('No. and Kind of Packages')} ${t('should be greater than 0')}`),
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <Numeric
                                            onChange={() => {}}
                                            decimalScale={5}
                                            disable={true}
                                            isAllowed={(inputObj: any) => {
                                                const { value } = inputObj;
                                                return value >= 0 && value <= 9999999;
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <FormSelectUnit
                                        label={'Unit'}
                                        name="carrierAssignmentInfoNoAndKindOfPackagesUnit"
                                        required={false}
                                        disabled={true}
                                        apiService={SERVICE.FREIGHT}
                                        prefixID="carrierAssignment"
                                        formItemProps={{
                                            noStyle: true,
                                        }}
                                    />
                                </Col>
                            </Row>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Gross Weight')}`} required={false}>
                            <Row gutter={8}>
                                <Col span={12}>
                                    <Form.Item
                                        noStyle
                                        name="carrierAssignmentInfoGrossWeight"
                                        rules={[
                                            { required: false, message: `${t('Gross Weight')} ${t('is required')}` },
                                            {
                                                validator: (_, value) => {
                                                    if (value?.length === 0) {
                                                        return Promise.resolve();
                                                    }
                                                    if (Number(value) <= 0) {
                                                        return Promise.reject(
                                                            new Error(`${t('Gross Weight')} ${t('should be greater than 0')}`),
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <Numeric
                                            onChange={() => {}}
                                            decimalScale={5}
                                            disable={true}
                                            isAllowed={(inputObj: any) => {
                                                const { value } = inputObj;
                                                return value >= 0 && value <= 9999999;
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item noStyle name="carrierAssignmentInfoGrossWeightUnit">
                                        <Input disabled></Input>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.TwoThirds>
                        <FormSelectPaymentTerm
                            disabled={true}
                            label={`${t('Payment Term')}`}
                            id={`carrierAssignmentInfoPaymentTerms`}
                            name={`carrierAssignmentInfoPaymentTerms`}
                            modalProps={{
                                title: `${t('Payment Term')}`,
                            }}
                            apiService={SERVICE.FREIGHT}
                            prefixID="carrierAssignment"
                        />
                    </ColLayout.TwoThirds>
                    <SharedPlanLoadShipmentIncotermDetail<ITFFormCarrierAssignmentInformation>
                        fields={{
                            shipmentIncotermSearch: {
                                name: 'carrierAssignmentInfoIncoterm',
                                required: false,
                                disabled: true,
                                apiService: SERVICE.FREIGHT,
                                prefixID: 'carrierAssignment',
                            },
                            shipmentIncotermPort: {
                                name: 'carrierAssignmentInfoIncotermPort',
                                required: false,
                                disabled: true,
                            },
                        }}
                    />
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item label={`${t('Freight Mode')}`} name="carrierAssignmentInfoFreightMode">
                                    <Select allowClear disabled>
                                        {FREIGHT_MODE?.map((item: any) => {
                                            return (
                                                <Option value={item} key={item}>
                                                    {item}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label={`${t('Freight Term')}`} name="carrierAssignmentInfoFreightTerm">
                                    <Select allowClear disabled>
                                        {FREIGHT_TERM?.map((item: any) => {
                                            return (
                                                <Option value={item} key={item}>
                                                    {item}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item
                            label={`${t('Freight Charge')}`}
                            name="carrierAssignmentInfoFreightCharge"
                            rules={[
                                {
                                    required: false,
                                    message: `${t('Freight Charge')} ${t('is required')}`,
                                },
                            ]}
                        >
                            <Radio.Group className="custom-space-radio" disabled>
                                <Radio value={FREIGHT_CHARGE.COLLECT}>{FREIGHT_CHARGE.COLLECT}</Radio>
                                <Radio value={FREIGHT_CHARGE.PREPAID}>{FREIGHT_CHARGE.PREPAID}</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item
                            label={`${t('Handling Charge')}`}
                            name="carrierAssignmentInfoHandlingCharge"
                            rules={[
                                {
                                    required: false,
                                    message: `${t('Handling Charge')} ${t('is required')}`,
                                },
                            ]}
                        >
                            <Radio.Group className="custom-space-radio" disabled>
                                <Radio value={HANDLING_CHARGE.COLLECT}>{HANDLING_CHARGE.COLLECT}</Radio>
                                <Radio value={HANDLING_CHARGE.PREPAID}>{HANDLING_CHARGE.PREPAID}</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>

                <RowLayout.ThreeCol style={{ marginBottom: 20 }}>
                    <ColLayout.Third>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item label={`${t('CY Stuffing Date')}`} name="carrierAssignmentInfoCYStuffingDate">
                                    <CustomDatePicker format="DD/MM/YYYY" style={{ width: '100%' }} placeholder="Select date" disabled />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    label={`${t('Stuffing Date')}`}
                                    name="carrierAssignmentInfoStuffingDate"
                                    rules={[
                                        {
                                            required: false,
                                            message: `${t('Stuffing Date')} ${t('is required')}`,
                                        },
                                    ]}
                                >
                                    <CustomDatePicker format="DD/MM/YYYY" style={{ width: '100%' }} placeholder="Select date" disabled />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item label={`${t('Return Load Date')}`} name="carrierAssignmentInfoReturnLoadDate">
                                    <CustomDatePicker format="DD/MM/YYYY" style={{ width: '100%' }} placeholder="Select date" disabled />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Row gutter={8}>
                            <Col span={12}>
                                <Form.Item label={`${t('ETD Date')}`} name="carrierAssignmentInfoETDDate">
                                    <CustomDatePicker format="DD/MM/YYYY" style={{ width: '100%' }} placeholder="Select date" disabled />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label={`${t('ETA Date')}`} name="carrierAssignmentInfoETADate">
                                    <CustomDatePicker format="DD/MM/YYYY" style={{ width: '100%' }} placeholder="Select date" disabled />
                                </Form.Item>
                            </Col>
                        </Row>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
            </Container>
            <RowLayout.OneCol style={{ marginTop: '20px' }}>
                <ColLayout.Full>{loadingType === LOADING_TYPE.CY && <CarrierAssignmentStuffingPlace />}</ColLayout.Full>
            </RowLayout.OneCol>
            <Container
                style={{
                    marginTop: '20px',
                }}
            >
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Tax Type')}`} name="carrierAssignmentInfoStuffingPlaceTaxType">
                            <Input disabled></Input>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item
                            label={`${t('Gen Set')}`}
                            name="carrierAssignmentInfoStuffingPlaceGenSet"
                            rules={[
                                {
                                    required: false,
                                    message: `${t('Gen Set')} ${t('is required')}`,
                                },
                            ]}
                        >
                            <Radio.Group className="custom-space-radio" disabled>
                                <Radio value={YES_NO.YES}>{`${t(YES_NO.YES)}`}</Radio>
                                <Radio value={YES_NO.NO}>{`${t(YES_NO.NO)}`}</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <FormRadioLoadingType
                            label={`${t('Loading Type')}`}
                            name="carrierAssignmentInfoStuffingPlaceLoadingType"
                            className="custom-space-radio"
                            rules={[
                                {
                                    required: false,
                                    message: `${t('Loading Type')} ${t('is required')}`,
                                },
                            ]}
                            disabled={true}
                        />
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <FormSelectTransportationAgent
                            label={t('Transportation Agent')}
                            name="carrierAssignmentInfoStuffingPlaceTransportationAgent"
                            disabled={true}
                            apiService={SERVICE.FREIGHT}
                            prefixID="carrierAssignment"
                        />
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Factory Contact')}`} name="carrierAssignmentInfoStuffingPlaceFactoryContact">
                            <Input disabled></Input>
                        </Form.Item>
                    </ColLayout.Third>
                    <ColLayout.Third>
                        <Form.Item label={`${t('Axle Type')}`} name="carrierAssignmentInfoStuffingPlaceAxleType">
                            <Radio.Group className="custom-space-group-radio" disabled>
                                <Radio id="freight_shipDetailradioCFS" value={AXLE_TYPE.NA}>{`${t(AXLE_TYPE.NA)}`}</Radio>
                                <Radio id="freight_shipDetailradioCY" value={AXLE_TYPE.TWO}>{`${t(AXLE_TYPE.TWO)}`}</Radio>
                                <Radio id="freight_shipDetailradioCY" value={AXLE_TYPE.THREE}>{`${t(AXLE_TYPE.THREE)}`}</Radio>
                            </Radio.Group>
                        </Form.Item>
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Third>
                        <FormSelectVendorGeneral
                            label={t('CY Billing To')}
                            name="carrierAssignmentInfoStuffingPlaceCYBillingTo"
                            disabled={true}
                            apiService={SERVICE.FREIGHT}
                            prefixID="carrierAssignment"
                        />
                    </ColLayout.Third>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Half style={{ marginBottom: 20 }}>
                        <CustomFormItemTextArea
                            label={t('CY Remark')}
                            name="carrierAssignmentInfoStuffingPlaceCYRemark"
                            disabled={true}
                            maxByte={500}
                        />
                    </ColLayout.Half>
                    <ColLayout.Half style={{ marginBottom: 20 }}>
                        <CustomFormItemTextArea
                            label={t('Requisition Remark')}
                            name="carrierAssignmentInfoStuffingPlaceRequisitionRemark"
                            disabled={true}
                            maxByte={200}
                        />
                    </ColLayout.Half>
                </RowLayout.ThreeCol>
                <RowLayout.ThreeCol>
                    <ColLayout.Half>
                        <Form.Item
                            label={`${t('Additional Condition (from Plan Load)')}`}
                            name="carrierAssignmentInfoStuffingPlaceAdditionalCondition"
                            rules={[
                                () => ({
                                    validator: (_, val) => validateMaxByte(val, 3000, t('Additional Condition (from Plan Load)')),
                                }),
                            ]}
                        >
                            <TextArea disabled={true} />
                        </Form.Item>
                    </ColLayout.Half>
                    <ColLayout.Half>
                        <Form.Item
                            label={`${t('Shipment Remark')}`}
                            name="carrierAssignmentInfoStuffingPlaceShipmentRemark"
                            rules={[
                                () => ({
                                    validator: (_, val) => validateMaxByte(val, 3000, t('Shipment Remark')),
                                }),
                            ]}
                        >
                            <TextArea disabled={true} />
                        </Form.Item>
                    </ColLayout.Half>
                </RowLayout.ThreeCol>
            </Container>
        </>
    );
};
export default CarrierAssignmentShipmentDetail;
