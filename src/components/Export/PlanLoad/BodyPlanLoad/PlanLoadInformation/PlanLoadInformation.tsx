import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import type { FormInstance } from 'antd';
import { Col, DatePicker, Form, Input, Radio, Row } from 'antd';

import { ButtonPrimaryLightOutline, ButtonSecondary } from 'common/Button';
import { CustomDatePicker } from 'common/CustomDatePicker/CustomDatePicker';
import { Numeric } from 'common/InputNumber/index';
import { customizeRequiredMark } from 'common/LabelDefault/LabelDefault';
import ModalDanger from 'common/Modal/ModalDanger/ModalDanger';

import { EXPORT_TYPES } from 'constants/Export/SalesOrder/salesOrder.constant';
import { SYS0001_TECHNICAL_ERROR } from 'constants/errorCode.constant';
import { ACTION, INITIATIVE_CURRENT_STATUS } from 'constants/initiative.constant';
import type { TypeAction } from 'types/Action.type';
import type { ITFFormPlanLoad } from 'types/Export/PlanLoad/Form/FormPlanLoad.type';
import type { ITFApproveStatus } from 'types/Export/PlanLoad/PlanLoad.type';
import type { ITFGetFinLcNo } from 'types/Export/PlanLoad/SavePlanLoad.type';

import { getFinLcNo, patchBookInvoice } from 'api/Export/PlanLoad/planLoadInformation';
import { defaultEmpty, defaultUndefined } from 'helper/ConvertData';
import { convertToDayjs } from 'helper/ConvertDate';
import { handleDisableFieldsByCreditPass, isPaymentTermLC } from 'helper/Export/PlanLoad';
import { maxByte, validateMaxByte } from 'helper/Validators';
import { useErrorCommon } from 'hooks/common/useErrorCommon';
import { usePlanLoadFieldControlInformation } from 'hooks/export/plan-load/access-control/usePlanLoadFieldControlInformation';
import { usePlanLoadFetchData } from 'hooks/export/plan-load/usePlanLoadFetchData';
import { usePlanLoadInformation } from 'hooks/export/plan-load/usePlanLoadInformation';
import { useCheckAmendment } from 'hooks/export/sales-order/useCheckAmendment';
import {
    planLoadInformationSelector,
    setDisableFieldsPlanLoadByBooking,
    setDisableFieldsPlanLoadPostToISO,
    setIsCheckFinLCNo,
    setIsClickValidate,
    setPlanLoadInvoiceNo,
} from 'store/slice/export/plan-load/planLoadInformation';

import ModalCheckedFINLC from './Modal/ModalCheckedFINLC';
import { Container } from './PlanLoadInformation.styles';

import { usePlanLoadContext } from 'contexts/Export/PlanLoad/PlanLoadContext';
import { usePlanLoadFieldControlContext } from 'contexts/Export/PlanLoad/PlanLoadFieldControlContext';
import { useInitiativeRouteParamsContext } from 'contexts/InitiativeRouteParamsContext';
import { usePageNavigationContext } from 'contexts/PageNavigationContext';
import { useTabMenuContext } from 'contexts/TabMenuContext';
import dayjs from 'dayjs';

export const rowGutterH = { xs: 0, sm: 0, md: 20, lg: 30, xl: 50, xxl: 100 };
export const rowGutterV = { xs: 5, sm: 5, md: 5, lg: 0, xl: 0, xxl: 0 };
export const colSpan = {
    span: 24,
    md: 12,
    lg: 8,
    xl: 8,
};

export const colSpan2 = {
    span: 24,
    md: 12,
    lg: 16,
    xl: 16,
};

interface StructProps {
    form: FormInstance<ITFFormPlanLoad>;
}

const initialState = {
    dataApproveStatus: {
        financial: undefined,
        sales: undefined,
        document: undefined,
    },
};

const PlanLoadInformation = (props: StructProps) => {
    const dispatch = useDispatch();
    const { t } = useTranslation();

    const { form } = props;
    const [showButtonClear, setShowButtonClear] = useState<boolean>(false);
    const [showButtonClearFinLC, setShowButtonClearFinLC] = useState<boolean>(false);
    const [errorMessage, setErrorMessage] = useState<string>('');
    const [showModalError, setShowModalError] = useState<boolean>(false);
    const [showFinLCNoField, setShowFinLCNoField] = useState<boolean>(false);
    const [showModalCheckedFINLC, setShowModalCheckedFINLC] = useState<boolean>(false);
    const [dataApproveStatus, setDataApproveStatus] = useState<ITFApproveStatus>(initialState.dataApproveStatus);
    const [loadingButtonCheckInvoiceNo, setLoadingButtonCheckInvoiceNo] = useState<boolean>(false);

    const { planLoadInfoId } = useInitiativeRouteParamsContext();
    const { isPreviewPath } = usePageNavigationContext();
    const { isSelectedTabFollowUp } = useTabMenuContext();
    const { currentStatus, isAssignedToOwnTeam } = usePlanLoadContext();
    const { isDisabledFieldPlanLoad } = usePlanLoadFieldControlContext();
    const { planLoadInformationDisabled } = usePlanLoadFieldControlInformation();
    const { fetchDataPlanLoadInfo } = usePlanLoadFetchData();
    const { checkBeingAmendment, clearBeingAmendment } = useCheckAmendment();
    const { action, disableFieldsByCreditPass, isClickValidate, dataResponsePlanLoad } = useSelector(planLoadInformationSelector);
    const { isDisableButtonBookingInvoicePlanLoad, isDisableButtonClearBookingInvoice } = usePlanLoadInformation();
    const formRequired = ![ACTION.SAVE_DRAFT, ACTION.PRE_SHIPMENT].includes(action as TypeAction);
    const disableField = !showButtonClear || isSelectedTabFollowUp || isPreviewPath || isDisabledFieldPlanLoad;

    const { alertError } = useErrorCommon();

    const required = {
        invoiceNo: true,
    };

    const setFormValues = () => {
        if (dataResponsePlanLoad) {
            const info = dataResponsePlanLoad.info ?? {};
            form.setFieldsValue({
                planLoadInfoSalesman: `${info?.salesman_code} - ${info?.salesman_name_eng}`,
                planLoadInfoOrganizationName: `${info?.org_code} - ${info?.org_name_eng}`,
                planLoadInfoSalesOrderDate: convertToDayjs(info?.sales_order_date),
                planLoadInfoCurrency: `${info?.currency_code} - ${info?.currency_name}`,
                planLoadInfoAgainstCurrency: `${info?.against_currency_code} - ${info?.against_currency_name}`,
                planLoadInfoCostRate: info?.cost_rate,
                planLoadInfoExportType: info?.export_type,
                planLoadInfoCompanyName: `${info?.company_code} - ${info?.company_name_eng}`,
                planLoadInfoProcessType: info?.process_type,
                planLoadInfoSalesOrderNo: info?.sales_order_no,
                planLoadInfoCostRateUnit: `${info?.against_currency_code} / ${info?.currency_code}`,
                planLoadInfoInvoiceNo: info?.invoice_no,
                planLoadInfoReferenceNo: info?.reference_no,
                planLoadInfoTriangleRef: info?.triangle_ref,
                planLoadInfoPlanLoadDate: convertToDayjs(info?.plan_load_date) ?? dayjs(),
                planLoadInfoFinLCNo: info?.fin_lc_no,
                planLoadInfoLCNo: info?.lc_no,
                planLoadInfoExpiryDate: convertToDayjs(info?.expiry_date),
                planLoadInfoLatestShipmentDate: convertToDayjs(info?.latest_shipment_date),
                planLoadInfoAdvisingBankCode: info?.advising_bank_code,
                planLoadInfoIssuingBank: info?.issuing_bank,
                planLoadInfoIssuingDate: convertToDayjs(info?.issuing_date),
            });
        }
    };

    const setShowButtonClearFinLCNo = () => {
        if (dataResponsePlanLoad?.info?.fin_lc_no) {
            setShowButtonClearFinLC(true);
        } else {
            handleFormClearFinLCNo();
            setShowButtonClearFinLC(false);
        }
    };

    useEffect(() => {
        if (dataResponsePlanLoad?.info?.invoice_no && isAssignedToOwnTeam) {
            setShowButtonClear(true);
            dispatch(setDisableFieldsPlanLoadByBooking(false));
        } else if (dataResponsePlanLoad?.info?.invoice_no) {
            dispatch(setDisableFieldsPlanLoadPostToISO(false));
        } else {
            setShowButtonClear(false);
            dispatch(setDisableFieldsPlanLoadByBooking(true));
            dispatch(setDisableFieldsPlanLoadPostToISO(true));
        }
        setFormValues();
        setShowButtonClearFinLCNo();

        const isShowFinLCNo = isPaymentTermLC(dataResponsePlanLoad?.customer_financial?.customer_paymentterm_name_eng);
        setShowFinLCNoField(isShowFinLCNo);
    }, [dataResponsePlanLoad, isAssignedToOwnTeam]);

    const handleBookingInvoiceNo = async () => {
        const formValue = form.getFieldsValue(['planLoadInfoInvoiceNo']);
        if (formValue?.planLoadInfoInvoiceNo?.trim() === '') {
            form.setFields([
                {
                    name: 'planLoadInfoInvoiceNo',
                    value: formValue?.planLoadInfoInvoiceNo?.trim(),
                },
            ]);
            return false;
        }

        try {
            await form.validateFields(['planLoadInfoInvoiceNo']);
        } catch (error: any) {
            return false;
        }
        setLoadingButtonCheckInvoiceNo(true);

        try {
            if (!dataResponsePlanLoad) {
                return false;
            }
            const invoiceNo = formValue?.planLoadInfoInvoiceNo?.trim();
            const dataBooking = {
                'company_code': dataResponsePlanLoad?.info?.company_code,
                'invoice_no': invoiceNo,
            };
            await patchBookInvoice(planLoadInfoId, dataBooking);
            await fetchDataPlanLoadInfo(planLoadInfoId, { readNotification: true });
            setShowButtonClear(true);
            dispatch(setDisableFieldsPlanLoadByBooking(false));
            dispatch(setDisableFieldsPlanLoadPostToISO(false));
            dispatch(setPlanLoadInvoiceNo(invoiceNo));
            return true;
        } catch (error: any) {
            form.setFields([
                {
                    name: 'planLoadInfoInvoiceNo',
                    errors: [t(`Invoice No. was used.`)],
                },
            ]);
            return false;
        } finally {
            setLoadingButtonCheckInvoiceNo(false);
        }
    };

    const handleClearInvoiceNo = async () => {
        if (!dataResponsePlanLoad) {
            return;
        }

        const invoiceNo = null;
        const dataBooking = {
            'company_code': dataResponsePlanLoad?.info?.company_code,
            'invoice_no': invoiceNo,
        };
        try {
            await patchBookInvoice(dataResponsePlanLoad?.info?.id ?? 0, dataBooking);
            setShowButtonClear(false);
            form.setFieldsValue({
                planLoadInfoInvoiceNo: null,
            });
            dispatch(setDisableFieldsPlanLoadByBooking(true));
            dispatch(setDisableFieldsPlanLoadPostToISO(true));
            dispatch(setPlanLoadInvoiceNo(invoiceNo));
            form.setFields([
                {
                    name: 'planLoadInfoFinLCNo',
                    errors: [],
                },
            ]);
        } catch (error: any) {
            alertError({
                e: error,
                onModalOpenedByCodeWithOptions: {
                    [SYS0001_TECHNICAL_ERROR]: {
                        handler: () => {
                            fetchDataPlanLoadInfo(planLoadInfoId);
                        },
                        delay: 500,
                    },
                },
            });
        }
    };

    const onClearInvoiceNo = () => {
        handleClearInvoiceNo();
        clearBeingAmendment();
    };

    const onClickBooking = async () => {
        const booked = await handleBookingInvoiceNo();
        if (!booked) {
            return;
        }
        await checkBeingAmendment();
        setLoadingButtonCheckInvoiceNo(false);
    };

    const handleShowButtonBookingAndClear = () => {
        if (showButtonClear) {
            return (
                <ButtonPrimaryLightOutline
                    text={t('Clear')}
                    id="buttonClearInvoiceNoPlanLoadInfo"
                    onClick={onClearInvoiceNo}
                    width="100%"
                    size="small"
                    style={{ height: '36.08px', maxWidth: '120px' }}
                    disabled={isDisableButtonClearBookingInvoice}
                />
            );
        }
        return (
            <ButtonSecondary
                text={t('Booking')}
                id={`buttonBookingInvoiceNoPlanLoadInfo`}
                onClick={onClickBooking}
                size="small"
                width="100%"
                style={{ height: '36.08px', maxWidth: '120px' }}
                disabled={isDisableButtonBookingInvoicePlanLoad}
                loading={loadingButtonCheckInvoiceNo}
            />
        );
    };

    const handleCheckFinLCNo = async () => {
        try {
            const values = await form.validateFields(['planLoadInfoFinLCNo']);
            const finLcNo = defaultEmpty(values.planLoadInfoFinLCNo).trim();
            form.setFieldsValue({ planLoadInfoFinLCNo: finLcNo });

            const dataCheckFinLcNo: ITFGetFinLcNo = {
                finLcNo,
                customerCode: dataResponsePlanLoad?.customer_detail?.customer_code,
                currencyCode: dataResponsePlanLoad?.info?.currency_code,
                companyCode: defaultUndefined(dataResponsePlanLoad?.info?.company_code),
                orgCode: dataResponsePlanLoad?.info?.org_code,
            };

            const res = await getFinLcNo(dataCheckFinLcNo);
            if (res.data) {
                setShowModalCheckedFINLC(true);
                const dataApprove: ITFApproveStatus = initialState.dataApproveStatus;
                res.data?.approve_status?.map((item: string, index: number) => {
                    const currentStatusTrim = item.split(':')[1].trim();
                    if (index == 0) {
                        dataApprove.financial = currentStatusTrim;
                    } else if (index == 1) {
                        dataApprove.sales = currentStatusTrim;
                    } else {
                        dataApprove.document = currentStatusTrim;
                    }
                });
                setDataApproveStatus(dataApprove);

                const { data } = res.data;
                const {
                    lc_no: lcNo,
                    expiry_date: expiryDate,
                    latest_shipment_date: latestShipmentDate,
                    advising_bank_code: advisingBankCode,
                    issuing_bank: issuingBank,
                    issuing_date: issuingDate,
                } = data;

                form.setFieldsValue({
                    planLoadInfoLCNo: lcNo,
                    planLoadInfoExpiryDate: convertToDayjs(expiryDate),
                    planLoadInfoLatestShipmentDate: convertToDayjs(latestShipmentDate),
                    planLoadInfoAdvisingBankCode: advisingBankCode,
                    planLoadInfoIssuingBank: issuingBank,
                    planLoadInfoIssuingDate: convertToDayjs(issuingDate),
                });

                setShowButtonClearFinLC(true);
            }
        } catch (error: any) {
            if (error.errorFields) {
                const errorMessage = error.errorFields.reduce((obj: any, item: any) => {
                    return item.errors[0];
                }, {});

                if (errorMessage === 'please check Fin L/C No.') {
                    form.setFields([
                        {
                            name: 'planLoadInfoFinLCNo',
                            errors: [],
                        },
                    ]);
                    dispatch(setIsClickValidate(false));
                    handleCheckFinLCNo();
                }
            }

            setDataApproveStatus(initialState.dataApproveStatus);
            if (error.response?.data?.details?.message) {
                setErrorMessage(error.response.data.details.message);
                setShowModalError(true);
            }
        }
    };

    const handleFormClearFinLCNo = () => {
        form.setFieldsValue({
            planLoadInfoFinLCNo: null,
            planLoadInfoLCNo: null,
            planLoadInfoExpiryDate: null,
            planLoadInfoLatestShipmentDate: null,
            planLoadInfoAdvisingBankCode: null,
            planLoadInfoIssuingBank: null,
            planLoadInfoIssuingDate: null,
        });
    };

    const handleClearFinLCNo = async () => {
        handleFormClearFinLCNo();
        setShowButtonClearFinLC(false);
    };

    const handleShowButtonCheckAndClear = () => {
        if (showButtonClearFinLC) {
            return (
                <ButtonPrimaryLightOutline
                    text={t('Clear')}
                    id={`buttonClearFinLcNoPlanLoadInfo`}
                    onClick={() => handleClearFinLCNo()}
                    width="100%"
                    size="small"
                    disabled={
                        handleDisableFieldsByCreditPass(disableField, disableFieldsByCreditPass) ||
                        isDisabledFieldPlanLoad ||
                        [INITIATIVE_CURRENT_STATUS.CANCELLED, INITIATIVE_CURRENT_STATUS.COMPLETED].includes(currentStatus)
                    }
                    style={{ height: '36.08px', maxWidth: '120px' }}
                />
            );
        }
        return (
            <ButtonSecondary
                text={t('Check')}
                id={`buttonCheckFinLcNoPlanLoadInfo`}
                onClick={() => handleCheckFinLCNo()}
                width="100%"
                disabled={disableField}
                size="small"
                htmlType="button"
                style={{ height: '36.08px', maxWidth: '120px' }}
            />
        );
    };

    function disabledDate(current: any) {
        return current && current < dayjs().startOf('day');
    }

    const onClosedModalError = () => {
        setShowModalError(false);
        form.setFieldsValue({
            planLoadInfoFinLCNo: null,
        });
    };

    const onCloseModalCheckedFinLC = () => {
        setShowModalCheckedFINLC(false);
    };

    useEffect(() => {
        dispatch(setIsCheckFinLCNo(showButtonClearFinLC));
    }, [showButtonClearFinLC]);

    return (
        <Container>
            <Form
                layout="vertical"
                scrollToFirstError={{
                    block: 'center',
                }}
                form={form}
                autoComplete="off"
                requiredMark={customizeRequiredMark}
            >
                <Row gutter={[rowGutterH, rowGutterV]}>
                    <Col {...colSpan}>
                        <Form.Item<ITFFormPlanLoad> id="planLoadInfoCompanyName" name="planLoadInfoCompanyName" label={t('Company Name')}>
                            <Input disabled={true} />
                        </Form.Item>
                    </Col>
                    <Col {...colSpan}>
                        <Form.Item<ITFFormPlanLoad>
                            id="planLoadInfoOrganizationName"
                            label={t('Organization Name')}
                            name="planLoadInfoOrganizationName"
                        >
                            <Input disabled></Input>
                        </Form.Item>
                    </Col>
                    <Col {...colSpan}>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item<ITFFormPlanLoad>
                                    id="planLoadInfoProcessType"
                                    name="planLoadInfoProcessType"
                                    label={t('Process Type')}
                                >
                                    <Input disabled={true} id="planLoadInfoProcessType" />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item<ITFFormPlanLoad> name="planLoadInfoExportType" label={t('Export Type')}>
                                    <Radio.Group
                                        style={{
                                            display: 'flex',
                                        }}
                                        disabled
                                    >
                                        <Radio id="planLoad_plInfoRadioExport" value={'Export'}>
                                            {t('Export')}
                                        </Radio>
                                        <Radio id="planLoad_plInfoRadioTriangle" value={'Triangle'}>
                                            {t('Triangle')}
                                        </Radio>
                                    </Radio.Group>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Col>
                    <Col {...colSpan}>
                        <Row gutter={10}>
                            <Col span={24}>
                                <Form.Item<ITFFormPlanLoad> label={t('Invoice No.')} required={required.invoiceNo}>
                                    <Row gutter={[10, 10]}>
                                        <Col span={14} sm={16}>
                                            <Form.Item<ITFFormPlanLoad>
                                                noStyle
                                                id="planLoadInfoInvoiceNo"
                                                name="planLoadInfoInvoiceNo"
                                                rules={[
                                                    {
                                                        required: required.invoiceNo,
                                                        message: `${t('Invoice No.')} ${t('is required')}`,
                                                    },
                                                    {
                                                        whitespace: true,
                                                        message: `${t('Invoice No.')} ${t('is required')}`,
                                                    },
                                                    {
                                                        validator(_, value) {
                                                            if (maxByte(value, 15)) {
                                                                return Promise.reject(new Error(`${t('Invoice No.')} ${t('is too long')}`));
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    id="planLoadInfoInvoiceNo"
                                                    disabled={showButtonClear || isDisableButtonBookingInvoicePlanLoad}
                                                />
                                            </Form.Item>
                                        </Col>
                                        <Col span={10} sm={8}>
                                            <Form.Item<ITFFormPlanLoad> noStyle>{handleShowButtonBookingAndClear()}</Form.Item>
                                        </Col>
                                    </Row>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Col>
                    <Col {...colSpan}>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item<ITFFormPlanLoad>
                                    id="planLoadInfoSalesOrderDate"
                                    name="planLoadInfoSalesOrderDate"
                                    label={t('Sales Order Date')}
                                >
                                    <DatePicker
                                        format="DD/MM/YYYY"
                                        style={{ width: '100%' }}
                                        disabled
                                        id="planLoadInfoSalesOrderDate"
                                        suffixIcon={<div></div>}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item<ITFFormPlanLoad>
                                    id="planLoadInfoPlanLoadDate"
                                    name="planLoadInfoPlanLoadDate"
                                    label={t('Plan Load Date')}
                                    rules={[
                                        {
                                            required: showButtonClear && formRequired,
                                            message: `${t('Plan Load Date')} ${t('is required')}`,
                                        },
                                    ]}
                                    className={showButtonClear ? '' : 'displayNoneAlert'}
                                >
                                    <CustomDatePicker
                                        format="DD/MM/YYYY"
                                        style={{ width: '100%' }}
                                        placeholder=""
                                        disabled={handleDisableFieldsByCreditPass(disableField, disableFieldsByCreditPass)}
                                        disabledDate={disabledDate}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Col>
                    <Col {...colSpan}>
                        <Form.Item<ITFFormPlanLoad>
                            id="planLoadInfoSalesOrderNo"
                            name="planLoadInfoSalesOrderNo"
                            label={t('Sales Order No.')}
                        >
                            <Input disabled={true} id="planLoadInfoSalesOrderNo" />
                        </Form.Item>
                    </Col>
                    <Col {...colSpan}>
                        <Form.Item<ITFFormPlanLoad> id="planLoadInfoSalesman" name="planLoadInfoSalesman" label={t('Salesman')}>
                            <Input disabled allowClear></Input>
                        </Form.Item>
                    </Col>
                    <Col {...colSpan}>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item<ITFFormPlanLoad>
                                    id="planLoadInfoCurrency"
                                    name="planLoadInfoCurrency"
                                    label={t('Currency')}
                                    rules={[
                                        {
                                            message: `${t('Currency')} ${t('is required')}`,
                                        },
                                    ]}
                                >
                                    <Input id="planLoadInfoCurrency" allowClear disabled></Input>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item<ITFFormPlanLoad>
                                    name="planLoadInfoAgainstCurrency"
                                    label={t('Against Currency')}
                                    rules={[
                                        {
                                            message: `${t('Against Currency')} ${t('is required')}`,
                                        },
                                    ]}
                                >
                                    <Input id="planLoadInfoAgainstCurrency" allowClear disabled></Input>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Col>
                    <Col {...colSpan}>
                        <Row gutter={10}>
                            <Col span={12}>
                                <Form.Item<ITFFormPlanLoad> id="planLoadInfoCostRate" name="planLoadInfoCostRate" label={t('Cost Rate')}>
                                    <Numeric disable id="planLoadInfoCostRate" decimalScale={2} />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item<ITFFormPlanLoad> id="planLoadInfoCostRateUnit" name="planLoadInfoCostRateUnit" label={t(' ')}>
                                    <Input id="planLoadInfoCostRateUnit" disabled />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Col>
                    <Col {...colSpan}>
                        <Form.Item
                            id="planLoadInfoReferenceNo"
                            name="planLoadInfoReferenceNo"
                            label={t('Reference No.')}
                            rules={[
                                () => ({
                                    validator: (_, val) => validateMaxByte(val, 15, t('Reference No.')),
                                }),
                            ]}
                        >
                            <Input
                                // disabled={handleDisableFieldsByCreditPass(disableField, disableFieldsByCreditPass)}
                                disabled={planLoadInformationDisabled.planLoadInfoReferenceNo}
                            />
                        </Form.Item>
                    </Col>
                    <Col {...colSpan}>
                        <Form.Item<ITFFormPlanLoad>
                            id="planLoadInfoTriangleRef"
                            name="planLoadInfoTriangleRef"
                            label={t('Triangle Ref.')}
                            rules={[
                                () => ({
                                    validator: (_, val) => validateMaxByte(val, 20, t('Triangle Ref.')),
                                }),
                                {
                                    required: dataResponsePlanLoad?.info?.export_type === EXPORT_TYPES.TRIANGLE ? formRequired : false,
                                    message: `${t('Triangle Ref.')} ${t('is required')}`,
                                },
                            ]}
                        >
                            <Input
                                // disabled={enableFieldsByRole(handleDisableFieldsByCreditPass(disableField, disableFieldsByCreditPass))}
                                disabled={planLoadInformationDisabled.planLoadInfoTriangleRef}
                            />
                        </Form.Item>
                    </Col>
                </Row>
                {showFinLCNoField && (
                    <Row gutter={[rowGutterH, rowGutterV]}>
                        <Col {...colSpan}>
                            <Row gutter={10}>
                                <Col span={24}>
                                    <Form.Item<ITFFormPlanLoad> label={t('Fin L/C No.')} required={formRequired && showButtonClear}>
                                        <Row gutter={[10, 10]}>
                                            <Col span={14} sm={16}>
                                                <Form.Item<ITFFormPlanLoad>
                                                    noStyle
                                                    id="planLoadInfoFinLCNo"
                                                    name="planLoadInfoFinLCNo"
                                                    rules={[
                                                        {
                                                            whitespace: true,
                                                            required: formRequired && showButtonClear,
                                                            message: `${t('Fin L/C No.')} ${t('is required')}`,
                                                        },
                                                        {
                                                            validator(_, value) {
                                                                if (maxByte(value, 15)) {
                                                                    return Promise.reject(
                                                                        new Error(`${t('Fin L/C No.')} ${t('is too long')}`),
                                                                    );
                                                                }
                                                                return Promise.resolve();
                                                            },
                                                        },
                                                        {
                                                            validator: (_, value) => {
                                                                if (isClickValidate && value) {
                                                                    if (showButtonClearFinLC) {
                                                                        return Promise.resolve();
                                                                    }
                                                                    return Promise.reject(new Error('please check Fin L/C No.'));
                                                                }
                                                                return Promise.resolve();
                                                            },
                                                        },
                                                    ]}
                                                >
                                                    <Input
                                                        onChange={() => dispatch(setIsClickValidate(false))}
                                                        disabled={handleDisableFieldsByCreditPass(
                                                            !showButtonClear || showButtonClearFinLC || isSelectedTabFollowUp,
                                                            disableFieldsByCreditPass,
                                                        )}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={10} sm={8}>
                                                <Form.Item<ITFFormPlanLoad> noStyle>{handleShowButtonCheckAndClear()}</Form.Item>
                                            </Col>
                                        </Row>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Col>
                        <Col {...colSpan}>
                            <Form.Item<ITFFormPlanLoad>
                                id="planLoadInfoLCNo"
                                name="planLoadInfoLCNo"
                                label={t('L/C No.')}
                                rules={[
                                    () => ({
                                        validator: (_, val) => validateMaxByte(val, 20, t('Triangle Ref.')),
                                    }),
                                ]}
                            >
                                <Input disabled={true} />
                            </Form.Item>
                        </Col>
                        <Col {...colSpan}>
                            <Row gutter={10}>
                                <Col span={12}>
                                    <Form.Item<ITFFormPlanLoad>
                                        id="planLoadInfoExpiryDate"
                                        name="planLoadInfoExpiryDate"
                                        label={t('Expiry Date')}
                                    >
                                        <DatePicker
                                            format="DD/MM/YYYY"
                                            style={{ width: '100%' }}
                                            disabled
                                            placeholder=""
                                            suffixIcon={<div></div>}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        id="planLoadInfoLatestShipmentDate"
                                        name="planLoadInfoLatestShipmentDate"
                                        label={t('Latest Shipment Date')}
                                    >
                                        <DatePicker
                                            format="DD/MM/YYYY"
                                            style={{ width: '100%' }}
                                            disabled
                                            placeholder=""
                                            suffixIcon={<div></div>}
                                        />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Col>
                        <Col {...colSpan}>
                            <Form.Item<ITFFormPlanLoad>
                                id="planLoadInfoAdvisingBankCode"
                                name="planLoadInfoAdvisingBankCode"
                                label={t('Advising Bank Code')}
                            >
                                <Input disabled></Input>
                            </Form.Item>
                        </Col>
                        <Col {...colSpan}>
                            <Form.Item<ITFFormPlanLoad>
                                id="planLoadInfoIssuingBank"
                                name="planLoadInfoIssuingBank"
                                label={t('Issuing Bank')}
                            >
                                <Input disabled></Input>
                            </Form.Item>
                        </Col>
                        <Col {...colSpan}>
                            <Form.Item<ITFFormPlanLoad>
                                id="planLoadInfoIssuingDate"
                                name="planLoadInfoIssuingDate"
                                label={t('Issuing Date')}
                            >
                                <DatePicker
                                    format="DD/MM/YYYY"
                                    style={{ width: '100%' }}
                                    disabled
                                    placeholder=""
                                    suffixIcon={<div></div>}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                )}
            </Form>
            <ModalDanger
                open={showModalError}
                onOk={() => onClosedModalError()}
                description={errorMessage}
                title={t('Error Fin L/C No.')}
            />
            <ModalCheckedFINLC
                isShowModal={showModalCheckedFINLC}
                onClose={() => onCloseModalCheckedFinLC()}
                dataApproveStatus={dataApproveStatus}
            />
        </Container>
    );
};

export default PlanLoadInformation;
