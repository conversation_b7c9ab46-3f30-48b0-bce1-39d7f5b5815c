import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

import type { TypeAction } from 'types/Action.type';
import type { ITFCarrier } from 'types/Carrier.type';
import type { ITFAirFreightBookingInfo } from 'types/Export/AirFreightBooking/AirFreightBookingInfo.type';
import type { ITFAirFreightBookingShipmentDetail } from 'types/Export/AirFreightBooking/AirFreightBookingShipmentDetail.type';
import type { ITFTableAirFreightBookingStuffingPlace } from 'types/Export/AirFreightBooking/AirFreightBookingStuffingPlace.type';

import type { RootState } from 'store/store';

export interface AirFreightBookingState {
    action: TypeAction;
    airFreightBookingInfo: ITFAirFreightBookingInfo;
    airFreightBookingShipmentDetail: ITFAirFreightBookingShipmentDetail;
    airFreightBookingStuffingPlaces: ITFTableAirFreightBookingStuffingPlace[];
    showAccept: boolean;
    selectedAgent: ITFCarrier;
}

const initialValues: AirFreightBookingState = {
    action: '',
    airFreightBookingInfo: {},
    airFreightBookingShipmentDetail: {},
    airFreightBookingStuffingPlaces: [],
    showAccept: false,
    selectedAgent: {},
};

const airFreightBookingSlice = createSlice({
    name: 'airFreightBooking',
    initialState: initialValues,
    reducers: {
        setAction: (state: AirFreightBookingState, action: PayloadAction<TypeAction>) => {
            state.action = action.payload;
        },
        setAirFreightBookingInfo: (state: AirFreightBookingState, action: PayloadAction<ITFAirFreightBookingInfo | undefined | null>) => {
            state.airFreightBookingInfo = action.payload ?? {};
        },
        setAirFreightBookingShipmentDetail: (
            state: AirFreightBookingState,
            action: PayloadAction<ITFAirFreightBookingShipmentDetail | undefined | null>,
        ) => {
            state.airFreightBookingShipmentDetail = action.payload ?? {};
        },
        setAirFreightBookingStuffingPlaces: (
            state: AirFreightBookingState,
            action: PayloadAction<ITFTableAirFreightBookingStuffingPlace[] | undefined | null>,
        ) => {
            state.airFreightBookingStuffingPlaces = action.payload ?? [];
        },
        setAirFreightBookingShowAccept: (state: AirFreightBookingState, action: PayloadAction<boolean>) => {
            state.showAccept = action.payload ?? {};
        },
        setAirFreightBookingSelectedAgent: (state: AirFreightBookingState, action: PayloadAction<ITFCarrier | null | undefined>) => {
            state.selectedAgent = action.payload ?? {};
        },
        resetAllStateAirFreightBooking: (state: AirFreightBookingState) => {
            state.action = initialValues.action;
            state.airFreightBookingInfo = initialValues.airFreightBookingInfo;
            state.airFreightBookingShipmentDetail = initialValues.airFreightBookingShipmentDetail;
            state.airFreightBookingStuffingPlaces = initialValues.airFreightBookingStuffingPlaces;
            state.showAccept = initialValues.showAccept;
            state.selectedAgent = initialValues.selectedAgent;
        },
    },
});

export const {
    setAction,
    setAirFreightBookingInfo,
    setAirFreightBookingShipmentDetail,
    setAirFreightBookingStuffingPlaces,
    setAirFreightBookingShowAccept,
    setAirFreightBookingSelectedAgent,
    resetAllStateAirFreightBooking,
} = airFreightBookingSlice.actions;
export const airFreightBookingSelector = (store: RootState) => store.airFreightBooking;

export default airFreightBookingSlice.reducer;
