import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';

import type { ITFCompany } from 'types/Company.type';
import type { ITFFlowName } from 'types/FlowName.type';
import type { ITFOrganization } from 'types/Organization.type';
import type {
    ITFDelegatePermission,
    ITFDelegatePermissionParams,
    ITFTableDelegatePermission,
    Metadata,
} from 'types/Setting/Permission/DelegatePermission.type';
import type { ITFUser } from 'types/User.type';

import { removeEmptyValuesFromObject } from 'helper/ConvertData';
import { transformDataDelegatePermission } from 'helper/Export/Setting/Permission/DelegatePermission';
import type { RootState } from 'store/store';

export interface SettingDelegatePermissionState {
    data: ITFDelegatePermission[];
    dataTable: ITFTableDelegatePermission[];
    total: number;
    metadata: Metadata;
    filter: ITFDelegatePermissionParams;
    selectedCompany?: ITFCompany;
    selectedOrg?: ITFOrganization;
    selectedStage?: string;
    selectedFlow?: ITFFlowName;
    selectedApprover?: ITFUser[];
    selectedSubstitute?: ITFUser;
    isLoadingBtn?: boolean;
    isLoadingTable?: boolean;
}

const initialValues: SettingDelegatePermissionState = {
    data: [],
    dataTable: [],
    total: 0,
    metadata: {
        filter: {
            startYear: [],
            startMonth: [],
            endYear: [],
            endMonth: [],
        },
    },
    filter: {
        startMonth: undefined,
        startYear: undefined,
        endMonth: undefined,
        endYear: undefined,
        current: 1,
    },
    selectedFlow: {},
    selectedCompany: {},
    selectedOrg: {},
    selectedApprover: [],
    selectedSubstitute: undefined,
    isLoadingBtn: false,
    isLoadingTable: false,
};

const settingDelegatePermissionSlice = createSlice({
    name: 'settingDelegatePermission',
    initialState: initialValues,
    reducers: {
        setDataDelegatePermission: (state: SettingDelegatePermissionState, action: PayloadAction<ITFDelegatePermission[]>) => {
            state.data = action.payload;
            state.dataTable = transformDataDelegatePermission(action.payload, state.filter.offset ?? 0);
        },
        setTotalDelegatePermission: (state, action: PayloadAction<number>) => {
            state.total = action.payload;
        },
        setMetadata: (state, action: PayloadAction<Metadata>) => {
            state.metadata = action.payload;
        },
        resetSearch: (state: SettingDelegatePermissionState) => {
            state.data = [];
            state.dataTable = [];
            state.total = 0;
            state.filter = {
                current: 1,
                startMonth: undefined,
                startYear: undefined,
                endMonth: undefined,
                endYear: undefined,
            };
            state.selectedFlow = {};
            state.selectedCompany = {};
            state.selectedOrg = {};
            state.selectedStage = '';
            state.selectedApprover = undefined;
            state.selectedSubstitute = [];
            state.isLoadingBtn = false;
            state.isLoadingTable = false;
        },
        setFilter: (state: SettingDelegatePermissionState, action: PayloadAction<ITFDelegatePermissionParams>) => {
            const obj = { ...state.filter, ...action.payload };
            state.filter = removeEmptyValuesFromObject(obj);
        },
        resetFilter: (state: SettingDelegatePermissionState) => {
            state.filter = {
                startMonth: undefined,
                startYear: undefined,
                endMonth: undefined,
                endYear: undefined,
                current: 1,
            };
        },
        setSelectedFlow: (state, action: PayloadAction<ITFFlowName>) => {
            state.selectedFlow = action.payload;
        },
        setSelectedCompany: (state: SettingDelegatePermissionState, action: PayloadAction<ITFCompany>) => {
            state.selectedCompany = action.payload;
        },
        setSelectedOrg: (state: SettingDelegatePermissionState, action: PayloadAction<ITFOrganization>) => {
            state.selectedOrg = action.payload;
        },
        setSelectedStage: (state, action: PayloadAction<string>) => {
            state.selectedStage = action.payload;
        },
        setSelectedSubstitute: (state, action: PayloadAction<ITFUser | undefined>) => {
            state.selectedSubstitute = action.payload;
        },
        setSelectedApprover: (state, action: PayloadAction<ITFUser[]>) => {
            state.selectedApprover = action.payload;
        },
        setIsLoadingBtn: (state, action: PayloadAction<boolean>) => {
            state.isLoadingBtn = action.payload;
        },
        setIsLoadingTable: (state, action: PayloadAction<boolean>) => {
            state.isLoadingTable = action.payload;
        },
    },
});

export const {
    setSelectedCompany,
    setSelectedOrg,
    setSelectedStage,
    setSelectedFlow,
    setSelectedApprover,
    setSelectedSubstitute,
    resetSearch,
    setMetadata,
    setFilter,
    resetFilter,
    setIsLoadingBtn,
    setIsLoadingTable,
    setTotalDelegatePermission,
    setDataDelegatePermission,
} = settingDelegatePermissionSlice.actions;
export const settingDelegatePermissionSelector = (store: RootState) => store.settingDelegatePermission;

export default settingDelegatePermissionSlice.reducer;
