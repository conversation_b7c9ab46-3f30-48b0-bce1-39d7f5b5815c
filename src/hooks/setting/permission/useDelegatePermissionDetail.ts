import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import type { FormInstance } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

import { DELEGATE_PERMISSION_KEY } from 'constants/Setting/Permission/delegatePermission.constant';
import type { ITFInitiativeRouteParams } from 'types/InitiativeRouteParams.type';
import type {
    ITFDelegatePermission,
    ITFSaveDelegatePermission,
    ITFTableDelegatePermission,
} from 'types/Setting/Permission/DelegatePermission.type';
import type { ITFDelegatePermissionForm } from 'types/Setting/Permission/DelegatePermissionForm.type';
import type { ITFGetUserParams, ITFUser } from 'types/User.type';

import { getDelegatePermissionById, patchDelegatePermission, postDelegatePermission } from 'api/Setting/Permission/delegatePermission';
import { getAPIDataUser } from 'api/Setting/Permission/user';
import { useCommon } from 'hooks/common/useCommon';
import { useDebounce } from 'hooks/common/useDebounce';
import { useErrorCommon } from 'hooks/common/useErrorCommon';
import { setLoading } from 'store/slice/initiative/initiative';
import {
    setSelectedApprover,
    setSelectedFlow,
    setSelectedStage,
    setSelectedSubstitute,
    settingDelegatePermissionDetailSelector,
} from 'store/slice/setting/permission/delegatePermissionDetail';
import { useAppDispatch } from 'store/store';

import camelcaseKeys from 'camelcase-keys';
import { useAlertNotificationContext } from 'contexts/AlertNotificationContext';
import dayjs from 'dayjs';

export const useDelegatePermissionDetail = (form?: FormInstance) => {
    const dispatch = useAppDispatch();
    const params = useParams<ITFInitiativeRouteParams>();
    const isEdit = useMemo(() => !!params.id, [params.id]);
    const history = useHistory();
    const [duplicatedUsernames, setDuplicatedUsernames] = useState<string[]>([]);

    const { t } = useTranslation();

    const [isActiveStatus, setIsActiveStatus] = useState(true);
    const { showSuccess: showSuccessSubmit } = useCommon(DELEGATE_PERMISSION_KEY.SUBMIT);
    const { alertError } = useErrorCommon();
    const { alertNotificationSuccess } = useAlertNotificationContext();
    const { debounce } = useDebounce();
    const [isCheckedAll, setIsCheckedAll] = useState(false);
    const [selectedOptionsOrg, setSelectedOptionsOrg] = useState<string[]>([]);
    const [dirty, setDirty] = useState(false);
    const [dataDelegatePermission, setDataDelegatePermission] = useState<ITFTableDelegatePermission>();

    const [selectedOptionsUser, setSelectedOptionsUser] = useState<string[]>([]);
    const [dataApproverList, setDataApproverList] = useState<ITFUser[]>([]);

    const { selectedStage, selectedFlow, selectedApprover, selectedSubstitute } = useSelector(settingDelegatePermissionDetailSelector);
    const onSelectStage = (stageValue: string) => {
        dispatch(setSelectedStage(stageValue));
        dispatch(setSelectedFlow(''));
        dispatch(setSelectedApprover([]));
        form?.setFieldsValue({
            flowName: undefined,
            activityName: undefined,
            searchApprover: undefined,
            searchSubstitute: undefined,
        });
    };

    const onSelectFlow = (flowValue: string) => {
        dispatch(setSelectedFlow(flowValue));
        dispatch(setSelectedApprover([]));
        dispatch(setSelectedSubstitute(undefined));
        form?.setFieldsValue({
            searchApprover: undefined,
            searchSubstitute: undefined,
        });
    };

    const onSelectApprover = (approverUser: ITFUser[]) => {
        dispatch(setSelectedApprover(approverUser));
    };

    const onSelectSubstitute = (substituteUsers: ITFUser) => {
        dispatch(setSelectedSubstitute(substituteUsers));
        form?.setFieldsValue({
            searchApprover: undefined,
        });
        setSelectedOptionsUser([]);
        if (substituteUsers.username) {
            fetchListAllUser({ limit: 1000, offset: 0 }, [substituteUsers.username]);
        }
    };

    const transformDataSaveDelegatePermission = (formValue: ITFDelegatePermissionForm): ITFSaveDelegatePermission => {
        const startDateTime = `${dayjs(formValue.startDate).format('YYYY-MM-DD')} ${dayjs(formValue.startTime).format('HH:mm')}:00.000`;
        const endDateTime = `${dayjs(formValue.endDate).format('YYYY-MM-DD')} ${dayjs(formValue.endTime).format('HH:mm')}:00.000`;

        return {
            flowName: formValue.flowName ?? '',
            activityName: formValue.activityName ?? '',
            approvers: formValue.searchApprover ?? [],
            substituteName: selectedSubstitute?.username ?? '',
            startDate: startDateTime,
            endDate: endDateTime,
        };
    };

    const handleDuplicateApproverError = (formValue: ITFDelegatePermissionForm, details: any) => {
        const duplicatedApprovers = details?.approvers ?? [];
        if (duplicatedApprovers.length > 0) {
            setDuplicatedUsernames(duplicatedApprovers); // ✅ เพิ่มบรรทัดนี้
            form?.setFields([
                {
                    name: 'searchApprover',
                    errors: [`${t('These approvers are duplicated')}: ${duplicatedApprovers.join(', ')}`],
                },
            ]);
        }
    };

    const onSubmit = async (formValue: ITFDelegatePermissionForm, action: string) => {
        dispatch(setLoading(true));
        try {
            const dataSave = transformDataSaveDelegatePermission(formValue);
            const response = await postDelegatePermission(dataSave);
            const data = response?.data;
            form?.setFieldsValue({ memoCode: data?.generalMemo?.memoCode });
            handleShowSuccess(action);
        } catch (error: any) {
            const data = error?.response?.data;
            if (data?.code === 'SYS0001') {
                handleDuplicateApproverError(formValue, data.details);
            }
            alertError({
                title: t('Error Submit'),
                code: data?.code,
                message: t(data?.message),
                goBack: false,
                showSupport: false,
            });
        } finally {
            dispatch(setLoading(false));
        }
    };

    const onUpdate = async (formValue: ITFDelegatePermissionForm, action: string) => {
        dispatch(setLoading(true));
        try {
            const dataSave = transformDataSaveDelegatePermission(formValue);
            await patchDelegatePermission(dataSave, Number(params.id));
            handleShowSuccess(action);
        } catch (error: any) {
            const data = error?.response?.data;
            alertError({
                title: t('Error Submit'),
                code: data?.code,
                message: t(data?.message),
                goBack: false,
                showSupport: false,
            });
        } finally {
            dispatch(setLoading(false));
        }
    };

    const handleShowSuccess = (action: string) => {
        switch (action) {
            case DELEGATE_PERMISSION_KEY.SUBMIT:
                showSuccessSubmit();
                break;
            case DELEGATE_PERMISSION_KEY.SUBMIT_AND_NEW:
                form?.resetFields([
                    'stage',
                    'flowName',
                    'activityName',
                    'searchApprover',
                    'searchSubstitute',
                    'startDate',
                    'startTime',
                    'endDate',
                    'endTime',
                ]);
                form?.setFieldsValue({ usedForData: null });
                setSelectedOptionsOrg([]);
                setIsCheckedAll(false);
                openNotificationSuccess();
                debounce(() => {
                    history.push('/setting/permission/delegate-permission/create');
                }, 1500);
                break;
            default:
                break;
        }
    };

    const fetchListAllUser = async (params: ITFGetUserParams, excludeUsernames: string[] = []) => {
        const filteredParams: ITFGetUserParams = {
            ...params,
            excludeUsername: excludeUsernames,
        };

        const res = await getAPIDataUser(filteredParams);
        if (res.data.data?.users.length === 1) {
            const selectedUser = res.data.data?.users[0];
            setSelectedOptionsUser([selectedUser.username ?? '']);

            form?.setFieldsValue({
                searchUser: [selectedUser.username],
            });
        }

        setDataApproverList(res.data.data.users);
    };

    const fetchDelegatePermissionDetail = async () => {
        try {
            const response = await getDelegatePermissionById(Number(params.id));
            const rawData = response?.data;
            let substitute: Object | undefined;
            let searchSubstituteValue: Object | undefined;
            if (!rawData) throw new Error('Data not found');
            const substituteInfo = rawData.substituteInfo ? JSON.parse(rawData.substituteInfo) : null;
            const approverUsers = (rawData.approvers || []).map((a: any) => {
                const info = a.approverInfo ? JSON.parse(a.approverInfo) : {};
                return {
                    username: a.approverName,
                    userId: info.user_id,
                    firstname: info.first_name,
                    surname: info.last_name,
                };
            });
            if (substituteInfo) {
                substitute = {
                    username: rawData.substituteName,
                    userId: substituteInfo.user_id,
                    firstname: substituteInfo.first_name,
                    surname: substituteInfo.last_name,
                };
            } else {
                substitute = undefined;
            }

            const camelData: ITFDelegatePermission = camelcaseKeys(
                {
                    ...rawData,
                    substitute,
                    approverUsers,
                },
                { deep: true },
            );

            if (camelData.substitute) {
                searchSubstituteValue = {
                    value: camelData.substitute.username,
                    label: `${camelData.substitute.userId} - ${camelData.substitute.firstname} ${camelData.substitute.surname}`,
                };
            } else {
                searchSubstituteValue = undefined;
            }

            setDataDelegatePermission(camelData);
            form?.setFieldsValue({
                stage: camelData.stage,
                flowName: camelData.flowName,
                activityName: camelData.activityName,
                searchApprover:
                    camelData.approverUsers?.map((user) => ({
                        value: user.username,
                        label: `${user.userId} - ${user.firstname} ${user.surname}`,
                    })) ?? [],
                searchSubstitute: searchSubstituteValue,
                startDate: dayjs(camelData.startDate),
                startTime: dayjs(camelData.startDate),
                endDate: dayjs(camelData.endDate),
                endTime: dayjs(camelData.endDate),
            });

            dispatch(setSelectedApprover(camelData.approverUsers ?? []));
            dispatch(setSelectedSubstitute(camelData.substitute ? (camelData.substitute as ITFUser) : undefined));
            dispatch(setSelectedFlow(camelData.flowName ?? ''));
            dispatch(setSelectedStage(camelData.stage ?? ''));

            if (camelData.substitute?.username) {
                fetchListAllUser({ limit: 1000, offset: 0 }, [camelData.substitute.username]);
            }
        } catch (error: any) {
            alertError({
                title: t('Error Get Delegate Permission Detail'),
                code: error?.data?.code,
                message: t(error?.data?.message),
                goBack: false,
                showSupport: false,
            });
        }
    };

    const onChangeSelectAll = (e: CheckboxChangeEvent) => {
        const isChecked = e.target.checked;
        const listAllApprover: string[] = dataApproverList?.map((item) => item.username ?? '');

        setIsCheckedAll(isChecked);

        if (isChecked) {
            setSelectedOptionsOrg(listAllApprover);
            form?.setFieldsValue({
                searchApprover: listAllApprover,
            });
        } else {
            setSelectedOptionsOrg([]);
            form?.setFieldsValue({
                searchApprover: undefined,
            });
        }
    };

    const handleChangeOptions = (value: string[], arrStr: string[]) => {
        setIsCheckedAll(arrStr.length === dataApproverList.length);
        setSelectedOptionsOrg(value);
        setDirty(true);
    };

    const openNotificationSuccess = () => {
        const text = isEdit ? t('Delegate Permission has been updated') : t('Delegate Permission has been created');
        alertNotificationSuccess(text);
    };

    return {
        isEdit,
        isActiveStatus,
        onSubmit,
        fetchDelegatePermissionDetail,
        setIsActiveStatus,
        onUpdate,
        onChangeSelectAll,
        isCheckedAll,
        handleChangeOptions,
        selectedOptionsOrg,
        dirty,
        setDirty,
        setSelectedOptionsOrg,
        setIsCheckedAll,
        dataDelegatePermission,
        onSelectStage,
        onSelectFlow,
        onSelectApprover,
        selectedStage,
        selectedFlow,
        selectedApprover,
        onSelectSubstitute,
        selectedSubstitute,
        selectedOptionsUser,
        dataApproverList,
        fetchListAllUser,
        duplicatedUsernames,
    };
};
