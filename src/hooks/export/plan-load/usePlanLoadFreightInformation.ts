import { useMemo, useState } from 'react';

import type { FormInstance } from 'antd';
import { Form } from 'antd';
import type { DatePickerProps } from 'antd/es/date-picker';
import type { TimePickerProps } from 'antd/es/time-picker';

import type { ITFFormPlanLoad } from 'types/Export/PlanLoad/Form/FormPlanLoad.type';

import { range } from 'helper/Array';
import { dateFormatter } from 'helper/ConvertDate';

import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);

export const usePlanLoadFreightInformation = (form?: FormInstance<ITFFormPlanLoad>) => {
    const [selectTimeETD, setSelectTimeETD] = useState<Dayjs | null>(null);
    const [selectTimeETA, setSelectTimeETA] = useState<Dayjs | null>(null);

    const stuffingDate = Form.useWatch('planLoadShipmentStuffingDate', form) ?? null;
    const dateETD = Form.useWatch('planLoadFreightInfoEtdDate', form) ?? null;
    const dateETA = Form.useWatch('planLoadFreightInfoEtaDate', form) ?? null;
    const timeETD = Form.useWatch('planLoadFreightInfoEtdTime', form) ?? null;
    const timeETA = Form.useWatch('planLoadFreightInfoEtaTime', form) ?? null;
    const isSameETDate = useMemo(() => dateETA && dateETD && dayjs(dateETA).isSame(dateETD, 'day'), [dateETA, dateETD]);

    const disableFieldDateETD = useMemo(() => !stuffingDate, [stuffingDate]);
    const disableFieldDateETA = useMemo(() => !dateETD, [dateETD]);

    const disableDateETD: DatePickerProps['disabledDate'] = (current) => {
        return current && dayjs(current).startOf('day') < dayjs(stuffingDate).startOf('day');
    };

    const disableDateETA: DatePickerProps['disabledDate'] = (current) => {
        return current && !!dateETD && current < dayjs(dateETD).startOf('day');
    };

    // TODO: used helper/DateTime.ts to replace this logic
    const disableTimeETD: TimePickerProps['disabledTime'] = () => {
        const etd = dateETD ? dateFormatter(dateETD) : '';
        const eta = dateETA ? dateFormatter(dateETA) : '';
        if (!dateETD || !dateETA || etd !== eta || !timeETA) {
            return {
                disabledHours: () => [],
                disabledMinutes: () => [],
            };
        }

        const hrETA = +dayjs(timeETA).format('HH');
        const hrETD = +dayjs(selectTimeETD).format('HH');
        const minuteETA = +dayjs(timeETA).format('mm');

        return {
            disabledHours: () => range(0, 24).splice(hrETA + 1, 24),
            disabledMinutes: () => (hrETD == hrETA ? range(0, 60).splice(minuteETA + 1, 60) : []),
        };
    };

    // TODO: used helper/DateTime.ts to replace this logic
    const disableTimeETA: TimePickerProps['disabledTime'] = () => {
        const etd = dateETD ? dateFormatter(dateETD) : '';
        const eta = dateETA ? dateFormatter(dateETA) : '';
        if (!dateETD || !dateETA || etd !== eta || !timeETD) {
            return {
                disabledHours: () => [],
                disabledMinutes: () => [],
            };
        }

        const hrETD = +dayjs(timeETD).format('HH');
        const hrETA = +dayjs(selectTimeETA).format('HH');
        const minuteETD = +dayjs(timeETD).format('mm');

        return {
            disabledHours: () => range(0, +dayjs(timeETD).format('HH')),
            disabledMinutes: () => (hrETD == hrETA ? range(0, minuteETD) : []),
        };
    };

    const validateETDateTime = () => {
        const formValues = form?.getFieldsValue();
        if (formValues?.planLoadFreightInfoEtdDate) {
            form?.validateFields(['planLoadFreightInfoEtdDate']);
        }
        if (formValues?.planLoadFreightInfoEtdTime) {
            form?.validateFields(['planLoadFreightInfoEtdTime']);
        }
        if (formValues?.planLoadFreightInfoEtaDate) {
            form?.validateFields(['planLoadFreightInfoEtaDate']);
        }
        if (formValues?.planLoadFreightInfoEtaTime) {
            form?.validateFields(['planLoadFreightInfoEtaTime']);
        }
    };

    const clearETDDateTimeFieldValue = () => {
        form?.setFieldsValue({
            planLoadFreightInfoEtdDate: null,
            planLoadFreightInfoEtdTime: null,
        });
    };

    const clearETADateTimeFieldValue = () => {
        form?.setFieldsValue({
            planLoadFreightInfoEtaDate: null,
            planLoadFreightInfoEtaTime: null,
        });
    };

    return {
        stuffingDate,
        dateETD,
        timeETD,
        dateETA,
        timeETA,
        isSameETDate,
        disableFieldDateETD,
        disableFieldDateETA,
        disableDateETD,
        disableTimeETD,
        disableDateETA,
        disableTimeETA,
        setSelectTimeETD,
        setSelectTimeETA,
        validateETDateTime,
        clearETDDateTimeFieldValue,
        clearETADateTimeFieldValue,
    };
};
