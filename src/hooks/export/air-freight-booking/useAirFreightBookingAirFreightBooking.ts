import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import type { DatePickerProps, TimePickerProps } from 'antd';
import { Form } from 'antd';

import type { ITFCarrier } from 'types/Carrier.type';
import type { TypeFormDatePicker } from 'types/Form.type';
import type { TypeNumberAlias } from 'types/TypeNumber.type';

import { dateFormatter } from 'helper/ConvertDate';
import { getDisabledTime } from 'helper/DateTime';
import { airFreightBookingSelector, setAirFreightBookingSelectedAgent } from 'store/slice/export/air-freight-booking/airFreightBooking';
import { useAppDispatch } from 'store/store';

import { useAirFreightBookingContext } from 'contexts/Export/AirFreightBooking/AirFreightBookingContext';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import isNil from 'lodash/isNil';

export const useAirFreightBookingAirFreightBooking = () => {
    const dispatch = useAppDispatch();
    const { form } = useAirFreightBookingContext();

    const { selectedAgent } = useSelector(airFreightBookingSelector);
    const [selectETATime, setSelectETATime] = useState<Dayjs | null>(null);

    const etdDate = Form.useWatch('airFreightBookingAirFreightBookingEtdDate', form) ?? null;
    const etaDate = Form.useWatch('airFreightBookingAirFreightBookingEtaDate', form) ?? null;
    const etdTime = Form.useWatch('airFreightBookingAirFreightBookingEtdTime', form) ?? null;
    const etaTime = Form.useWatch('airFreightBookingAirFreightBookingEtaTime', form) ?? null;
    const isSameETDate = useMemo(() => etaDate && etdDate && dayjs(etaDate).isSame(etdDate, 'day'), [etaDate, etdDate]);

    const disableFieldETADate = useMemo(() => !etdDate, [etdDate]);

    const disableETADate: DatePickerProps['disabledDate'] = (current) => {
        return current && !!etdDate && current < dayjs(etdDate).startOf('day');
    };

    const disableEndTime: TimePickerProps['disabledTime'] = getDisabledTime({
        startDate: etaDate,
        endDate: etdDate,
        referenceTime: etdTime,
        selectedTime: selectETATime,
        isStartTime: false,
        dateFormatter,
    });

    const onClearETADateTimeFieldValue = () => {
        form?.setFieldsValue({
            airFreightBookingAirFreightBookingEtaDate: null,
            airFreightBookingAirFreightBookingEtaTime: null,
        });
    };

    const validateETDAndETADateTime = () => {
        const formValues = form?.getFieldsValue();
        const {
            airFreightBookingAirFreightBookingEtdDate,
            airFreightBookingAirFreightBookingEtdTime,
            airFreightBookingAirFreightBookingEtaDate,
            airFreightBookingAirFreightBookingEtaTime,
        } = formValues || {};

        if (airFreightBookingAirFreightBookingEtdDate) {
            form?.validateFields(['airFreightBookingAirFreightBookingEtdDate']);
        }
        if (airFreightBookingAirFreightBookingEtdTime) {
            form?.validateFields(['airFreightBookingAirFreightBookingEtdTime']);
        }
        if (airFreightBookingAirFreightBookingEtaDate) {
            form?.validateFields(['airFreightBookingAirFreightBookingEtaDate']);
        }
        if (airFreightBookingAirFreightBookingEtaTime) {
            form?.validateFields(['airFreightBookingAirFreightBookingEtaTime']);
        }
    };

    const handleSetAirFreightBookingDueDate = (etdDate: TypeFormDatePicker, terms: TypeNumberAlias) => {
        const dueDate = etdDate && !isNil(terms) ? dayjs(etdDate).add(terms, 'day') : null;
        form.setFieldsValue({ airFreightBookingAirFreightBookingDueDate: dueDate });
    };

    const onSelectAirFreightBookingAgent = (value: ITFCarrier) => {
        handleSetAirFreightBookingDueDate(etdDate, value?.terms);
        dispatch(setAirFreightBookingSelectedAgent(value));
    };

    const onChangeAirFreightBookingETDDate = (date: TypeFormDatePicker, dateString: string | string[]) => {
        onClearETADateTimeFieldValue();
        validateETDAndETADateTime();
        handleSetAirFreightBookingDueDate(date, selectedAgent?.terms);
    };

    return {
        etdDate,
        etaDate,
        disableETADate,
        disableFieldETADate,
        onClearETADateTimeFieldValue,
        setSelectETATime,
        isSameETDate,
        etaTime,
        etdTime,
        validateETDAndETADateTime,
        disableEndTime,
        onSelectAirFreightBookingAgent,
        onChangeAirFreightBookingETDDate,
    };
};
