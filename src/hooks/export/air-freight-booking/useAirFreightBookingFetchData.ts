import { useDispatch } from 'react-redux';

import { HANDLING_CHARGE, LOADING_TYPE, PRODUCT_TYPE } from 'constants/shipment.constant';
import type { ITFAirFreightBooking } from 'types/Export/AirFreightBooking/AirFreightBooking.type';
import type { ITFTableAirFreightBookingStuffingPlace } from 'types/Export/AirFreightBooking/AirFreightBookingStuffingPlace.type';
import type { ITFFormAirFreightBooking } from 'types/Export/AirFreightBooking/Form/FormAirFreightBooking.type';
import type { ITFFetchDataOptions } from 'types/FetchData.type';

import { getAirFreightBookingInfo } from 'api/Export/AirFreightBooking/airFreightBooking';
import {
    convertToFormDatePicker,
    convertToFormInputText,
    convertToFormNumeric,
    convertToFormRadio,
    convertToFormSelectCodeName,
    convertToFormTimePicker,
} from 'helper/Common/TransformApiDataToFields';
import { dateFormatter } from 'helper/ConvertDate';
import { delay } from 'helper/Delay';
import { transformAirFreightBookingStuffingPlace } from 'helper/Export/AirFreightBooking/AirFreightBookingStuffingPlace.helper';
import { generateShipmentStuffingPlaceFieldName } from 'helper/Export/ShipmentStuffingPlace.helper';
import { useErrorCommon } from 'hooks/common/useErrorCommon';
import {
    setAirFreightBookingInfo,
    setAirFreightBookingSelectedAgent,
    setAirFreightBookingShipmentDetail,
    setAirFreightBookingShowAccept,
    setAirFreightBookingStuffingPlaces,
} from 'store/slice/export/air-freight-booking/airFreightBooking';
import { setLoading } from 'store/slice/initiative/initiative';

import { useAirFreightBookingNotification } from './useAirFreightBookingNotification';

import { useAirFreightBookingContext } from 'contexts/Export/AirFreightBooking/AirFreightBookingContext';
import { freightMasterDataApiSlice } from 'services/export/freight/freightMasterData';

export const useAirFreightBookingFetchData = () => {
    const dispatch = useDispatch();

    const { form } = useAirFreightBookingContext();
    const { alertError } = useErrorCommon();
    const { readNotificationAirFreightBooking, readNotificationOnInitialAirFreightBooking } = useAirFreightBookingNotification();

    const [lazyGetFreightMasterDataCommodityQuery] = freightMasterDataApiSlice.useLazyGetFreightMasterDataCommodityQuery();
    const [lazyGetFreightMasterDataCarrierQuery] = freightMasterDataApiSlice.useLazyGetFreightMasterDataCarrierQuery();
    const [lazyGetFreightMasterDataUnitQuery] = freightMasterDataApiSlice.useLazyGetFreightMasterDataUnitQuery();

    const handleSetAirFreightBookingData = async (data: ITFAirFreightBooking) => {
        dispatch(setAirFreightBookingInfo(data.airFreightBookingInfo));
        dispatch(setAirFreightBookingInfo(data.airFreightBookingInfo));
        dispatch(setAirFreightBookingShipmentDetail(data.airFreightBookingShipmentDetail));
        dispatch(setAirFreightBookingShowAccept(data.showAccept ?? false));
        dispatch(setAirFreightBookingSelectedAgent(data?.airFreightBookingInfo?.agentCarrierData));
        await delay(800);
    };

    const handleSetAirFreightBookingForm = (data: ITFAirFreightBooking) => {
        handleSetFormAirFreightBookingInformation(data);
        handleSetFormAirFreightBookingAirFreightBooking(data);
    };

    const handleSetFormAirFreightBookingInformation = (data: ITFAirFreightBooking) => {
        const { airFreightBookingInfo: info, airFreightBookingShipmentDetail: shipment } = data;
        form?.setFieldsValue({
            // freight info
            airFreightBookingInfoCompanyName: convertToFormSelectCodeName(info?.companyCode, info?.companyNameEng),
            airFreightBookingInfoOrganization: convertToFormSelectCodeName(info?.orgCode, info?.orgNameEng),
            airFreightBookingInfoBookingStatus: convertToFormInputText(info?.bookingStatus),
            airFreightBookingInfoRequisitionDate: convertToFormDatePicker(info?.requestDate),
            airFreightBookingInfoMainInvoiceNo: convertToFormInputText(info?.invoiceNo),
            airFreightBookingInfoJobNo: convertToFormInputText(info?.jobNo),
            airFreightBookingInfoJobDate: convertToFormDatePicker(info?.jobDate),
            airFreightBookingInfoConfirmDate: convertToFormDatePicker(info?.confirmDate),
            airFreightBookingInfoShipmentNo: convertToFormInputText(info?.shipmentNo),
            airFreightBookingInfoConsigneeName: convertToFormSelectCodeName(info?.consigneeCode, info?.consigneeName),
            airFreightBookingInfoCoLoadInvoiceFromPlanLoad: convertToFormInputText(info?.coLoadInvoice),
            // shipment detail
            airFreightBookingShipmentTradeCommodity: convertToFormSelectCodeName(shipment?.commodityCode, shipment?.commodityDesc),
            airFreightBookingShipmentShipBy: convertToFormInputText(shipment?.shipBy),
            airFreightBookingShipmentProductType: convertToFormRadio(shipment?.productType, [PRODUCT_TYPE.SAMPLE, PRODUCT_TYPE.NORMAL]),
            airFreightBookingShipmentLoadingPortCode: convertToFormInputText(shipment?.loadingPortCode),
            airFreightBookingShipmentLoadingPortName: convertToFormInputText(shipment?.loadingPortName),
            airFreightBookingShipmentLoadingCountryCode: convertToFormInputText(shipment?.loadingCountryCode),
            airFreightBookingShipmentLoadingCountryName: convertToFormInputText(shipment?.loadingCountryName),
            airFreightBookingShipmentDestinationPortCode: convertToFormInputText(shipment?.destinationPortCode),
            airFreightBookingShipmentDestinationPortName: convertToFormInputText(shipment?.destinationPortName),
            airFreightBookingShipmentDestinationCountryCode: convertToFormInputText(shipment?.destinationCountryCode),
            airFreightBookingShipmentDestinationCountryName: convertToFormInputText(shipment?.destinationCountryName),
            airFreightBookingShipmentLoadingType: convertToFormRadio(shipment?.loadingType, [LOADING_TYPE.CFS, LOADING_TYPE.CY]),
            airFreightBookingShipmentDimension: convertToFormInputText(shipment?.dimension),
            airFreightBookingShipmentCubicMeters: convertToFormNumeric(shipment?.cubicMeters),
            airFreightBookingShipmentCubicMetersUnit: convertToFormInputText(shipment?.cubicMetersUnit),
            airFreightBookingShipmentHandlingCharge: convertToFormRadio(shipment?.handlingCharge, [
                HANDLING_CHARGE.COLLECT,
                HANDLING_CHARGE.PREPAID,
            ]),
            airFreightBookingShipmentNoAndKindOfPackages: convertToFormNumeric(shipment?.noOfPackage),
            airFreightBookingShipmentNoAndKindOfPackagesUnit: convertToFormInputText(shipment?.noOfPackageUnit),
            airFreightBookingShipmentGrossWeight: convertToFormNumeric(shipment?.grossWeight),
            airFreightBookingShipmentGrossWeightUnit: convertToFormInputText(shipment?.grossUnit),
            airFreightBookingShipmentETDDate: convertToFormDatePicker(shipment?.etdDatetime),
            airFreightBookingShipmentETADate: convertToFormDatePicker(shipment?.etaDatetime),
            // remark
            airFreightBookingInfoRequisitionRemark: convertToFormInputText(shipment?.requisitionRemark),
            airFreightBookingInfoAdditionalConditionFromPlanLoad: convertToFormInputText(shipment?.additionalCondition),
            airFreightBookingInfoShipmentRemark: convertToFormInputText(shipment?.shipmentRemark),
        });
    };

    const handleSetFormAirFreightBookingAirFreightBooking = (data: ITFAirFreightBooking) => {
        const { airFreightBookingInfo: info } = data;
        const { commodityCode, commodityDesc } = info ?? {};
        form?.setFieldsValue({
            // air freight booking info
            airFreightBookingAirFreightBookingCommodity: convertToFormSelectCodeName(commodityCode, commodityDesc),
            airFreightBookingAirFreightBookingCommodityCode: convertToFormInputText(commodityCode),
            airFreightBookingAirFreightBookingCommodityName: convertToFormInputText(commodityDesc),
            airFreightBookingAirFreightBookingAgent: convertToFormInputText(info?.agentCarrierCode),
            airFreightBookingAirFreightBookingAgentName: convertToFormInputText(info?.agentCarrierName),
            airFreightBookingAirFreightBookingEtdDate: convertToFormDatePicker(info?.etdDatetime),
            airFreightBookingAirFreightBookingEtdTime: convertToFormTimePicker(info?.etdTime),
            airFreightBookingAirFreightBookingEtaDate: convertToFormDatePicker(info?.etaDatetime),
            airFreightBookingAirFreightBookingEtaTime: convertToFormTimePicker(info?.etaTime),
            airFreightBookingAirFreightBookingDueDate: convertToFormDatePicker(info?.dueDate),
            airFreightBookingAirFreightBookingStuffingDate: convertToFormDatePicker(info?.stuffingDate),
            airFreightBookingAirFreightBookingAwbNo: convertToFormInputText(info?.awbNo),
            airFreightBookingAirFreightBookingHawbNo: convertToFormInputText(info?.hawbNo),
            airFreightBookingAirFreightBookingBookingRefNo: convertToFormInputText(info?.bookingRef),
            airFreightBookingAirFreightBookingAwbDate: convertToFormDatePicker(info?.awbDate),
            airFreightBookingAirFreightBookingConnectingAt: convertToFormInputText(info?.connectingAt),
            airFreightBookingAirFreightBookingChargeWeight: convertToFormNumeric(info?.chargeWeightQuantity),
            airFreightBookingAirFreightBookingChargeWeightUnit: convertToFormInputText(info?.chargeWeightUnit),
            airFreightBookingAirFreightBookingDryIce: convertToFormNumeric(info?.dryIceQuantity),
            airFreightBookingAirFreightBookingDryIceUnit: convertToFormInputText(info?.dryIceUnit),
            airFreightBookingAirFreightBookingTaxType: convertToFormSelectCodeName(info?.taxTypeCode, info?.taxTypeName),
            // job booking
            airFreightBookingJobBookingAirLine: convertToFormInputText(info?.airLine),
            airFreightBookingJobBookingFlightNo: convertToFormInputText(info?.flightNo),
            airFreightBookingJobBookingSecondAirLine: convertToFormInputText(info?.secondAirLine),
            airFreightBookingJobBookingSecondFlightNo: convertToFormInputText(info?.secondFlightNo),
            airFreightBookingJobBookingConfirmBookingDate: convertToFormInputText(
                dateFormatter(info?.confirmBookingDate, 'DD/MM/YYYY HH:mm:ss'),
            ),
        });
    };

    const handleSetAirFreightBookingInfoStuffingPlace = (data: ITFAirFreightBooking) => {
        const stuffingPlaces = transformAirFreightBookingStuffingPlace(data.airFreightBookingStuffingPlace?.data ?? []);
        dispatch(setAirFreightBookingStuffingPlaces(stuffingPlaces));
        stuffingPlaces.forEach((item) => {
            const fieldName = generateShipmentStuffingPlaceFieldName<ITFFormAirFreightBooking, ITFTableAirFreightBookingStuffingPlace>(
                'airFreightBookingInfoStuffingPlaces',
                `${item.key}`,
                'remark',
            );
            form.setFieldsValue({
                [fieldName]: item.remark,
            });
        });
    };

    const fetchDataAirFreightBookingInfo = async (
        airFreightBookingInfoId: number,
        options?: ITFFetchDataOptions,
    ): Promise<ITFAirFreightBooking | null> => {
        try {
            const { data } = await getAirFreightBookingInfo(airFreightBookingInfoId);
            handleSetAirFreightBookingData(data);
            handleSetAirFreightBookingForm(data);
            handleSetAirFreightBookingInfoStuffingPlace(data);
            if (options?.readNotification) {
                readNotificationAirFreightBooking(data);
            }
            return data;
        } catch (error: any) {
            console.log(error);
            alertError({
                e: error,
                goBack: false,
                showSupport: false,
            });
            return null;
        }
    };

    const fetchAllDataAirFreightBooking = async (airFreightBookingInfoId: number, options?: ITFFetchDataOptions) => {
        try {
            const [data] = await Promise.all([fetchDataAirFreightBookingInfo(airFreightBookingInfoId)]);
            if (options?.readNotification) {
                readNotificationAirFreightBooking(data);
            }
            return { data };
        } catch (error: any) {
            console.log(error);
            return { data: null };
        }
    };

    const fetchMasterDataOnInitialAirFreightBooking = () => {
        const query = { limit: 10, offset: 0, search: '' };
        lazyGetFreightMasterDataCommodityQuery(query);
        lazyGetFreightMasterDataCarrierQuery(query);
        lazyGetFreightMasterDataUnitQuery(query);
    };

    const initializeAirFreightBookingPage = async (airFreightBookingInfoId: number) => {
        try {
            dispatch(setLoading(true));
            const { data } = await fetchAllDataAirFreightBooking(airFreightBookingInfoId);
            readNotificationOnInitialAirFreightBooking(data);
            fetchMasterDataOnInitialAirFreightBooking();
        } catch (error: any) {
            console.log(error);
        } finally {
            dispatch(setLoading(false));
        }
    };

    return {
        initializeAirFreightBookingPage,
        fetchAllDataAirFreightBooking,
        fetchDataAirFreightBookingInfo,
    };
};
