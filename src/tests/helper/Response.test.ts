import { filterEmptyParams, isContentTypeJSON, transformParams, transformResponse, transformResponseData } from 'helper/Response';

describe('isContentTypeJSON', () => {
    test.each([
        // Test case: response is undefined
        [undefined, false],

        // Test case: response is null
        [null, false],

        // Test case: response with no headers
        [{}, false],

        // Test case: response with empty headers
        [{ headers: {} }, false],

        // Test case: response with headers but no content-type
        [{ headers: { 'other-header': 'value' } }, false],

        // Test case: response with empty content-type
        [{ headers: { 'content-type': '' } }, false],

        // Test case: response with non-JSON content-type
        [{ headers: { 'content-type': 'text/html' } }, false],
        [{ headers: { 'content-type': 'text/plain; charset=utf-8' } }, false],

        // Test case: response with JSON content-type
        [{ headers: { 'content-type': 'application/json' } }, true],
        [{ headers: { 'content-type': 'application/json; charset=utf-8' } }, true],

        // Test case: response with JSON content-type in mixed case
        [{ headers: { 'content-type': 'Application/JSON' } }, true],

        // Test case: response with JSON content-type as part of a larger string
        [{ headers: { 'content-type': 'text/application/json+ld' } }, true],

        // Test case: response with content-type as array (edge case)
        // [{ headers: { 'content-type': ['application/json'] } }, true],
        // [{ headers: { 'content-type': ['text/html'] } }, false],
    ])('isContentTypeJSON(%p) should return %p', (response, expected) => {
        expect(isContentTypeJSON(response)).toBe(expected);
    });
});

describe('filterEmptyParams', () => {
    test('should return an empty object when given an empty object', () => {
        const params = {};
        const result = filterEmptyParams(params);
        expect(result).toEqual({});
    });

    test('should filter out null values', () => {
        const params = {
            id: 1,
            name: 'test',
            description: null,
        };
        const expected = {
            id: 1,
            name: 'test',
        };
        const result = filterEmptyParams(params);
        expect(result).toEqual(expected);
    });

    test('should filter out undefined values', () => {
        const params = {
            id: 1,
            name: 'test',
            description: undefined,
        };
        const expected = {
            id: 1,
            name: 'test',
        };
        const result = filterEmptyParams(params);
        expect(result).toEqual(expected);
    });

    test('should filter out both null and undefined values', () => {
        const params = {
            id: 1,
            name: null,
            description: undefined,
            status: 'active',
        };
        const expected = {
            id: 1,
            status: 'active',
        };
        const result = filterEmptyParams(params);
        expect(result).toEqual(expected);
    });

    test('should keep falsy values that are not null or undefined', () => {
        const params = {
            id: 0,
            name: '',
            isActive: false,
            description: null,
            tags: undefined,
        };
        const expected = {
            id: 0,
            name: '',
            isActive: false,
        };
        const result = filterEmptyParams(params);
        expect(result).toEqual(expected);
    });

    test('should handle nested objects correctly', () => {
        const params = {
            id: 1,
            details: {
                name: 'test',
                description: null,
            },
            tags: undefined,
        };
        const expected = {
            id: 1,
            details: {
                name: 'test',
                description: null,
            },
        };
        const result = filterEmptyParams(params);
        expect(result).toEqual(expected);
    });
});

describe('transformResponse', () => {
    test('should return response as is when response is undefined', () => {
        const result = transformResponse(undefined);
        expect(result).toBeUndefined();
    });

    test('should return response as is when response is null', () => {
        const result = transformResponse(null);
        expect(result).toBeNull();
    });

    test('should return response as is when response has no data property', () => {
        const response = { status: 200, headers: { 'content-type': 'application/json' } };
        const result = transformResponse(response);
        expect(result).toEqual(response);
    });

    test('should return response as is when content-type is not JSON', () => {
        const response = {
            data: { user_name: 'test', user_age: 30 },
            headers: { 'content-type': 'text/plain' },
        };
        const result = transformResponse(response);
        expect(result).toEqual(response);
        expect(result.data).toEqual({ user_name: 'test', user_age: 30 });
    });

    test('should transform response data keys to camelCase when content-type is JSON', () => {
        const response = {
            data: { user_name: 'test', user_details: { first_name: 'John', last_name: 'Doe' } },
            headers: { 'content-type': 'application/json' },
        };
        const result = transformResponse(response);
        expect(result.data).toEqual({
            userName: 'test',
            userDetails: { firstName: 'John', lastName: 'Doe' },
        });
    });

    test('should handle null or undefined data', () => {
        const response1 = {
            data: null,
            headers: { 'content-type': 'application/json' },
        };
        const response2 = {
            data: undefined,
            headers: { 'content-type': 'application/json' },
        };

        const result1 = transformResponse(response1);
        const result2 = transformResponse(response2);

        expect(result1.data).toBeNull();
        expect(result2.data).toBeUndefined();
    });

    test('should not modify the original response object', () => {
        const originalData = { user_name: 'test' };
        const response = {
            data: { ...originalData },
            headers: { 'content-type': 'application/json' },
        };

        transformResponse(response);
        expect(response.data).toEqual(originalData);
    });
});

describe('transformResponseData', () => {
    test('should convert string JSON data to object with camelCase keys', () => {
        const data = JSON.stringify({
            user_id: 1,
            user_name: 'test',
            user_details: {
                first_name: 'John',
                last_name: 'Doe',
            },
        });

        const expected = {
            userId: 1,
            userName: 'test',
            userDetails: {
                firstName: 'John',
                lastName: 'Doe',
            },
        };

        const result = transformResponseData(data);
        expect(result).toEqual(expected);
    });

    test('should convert object data with snake_case keys to camelCase', () => {
        const data = {
            user_id: 1,
            user_name: 'test',
            user_details: {
                first_name: 'John',
                last_name: 'Doe',
            },
        };

        const expected = {
            userId: 1,
            userName: 'test',
            userDetails: {
                firstName: 'John',
                lastName: 'Doe',
            },
        };

        const result = transformResponseData(data);
        expect(result).toEqual(expected);
    });

    test('should handle arrays in object data', () => {
        const data = {
            users: [
                { user_id: 1, user_name: 'test1' },
                { user_id: 2, user_name: 'test2' },
            ],
        };

        const expected = {
            users: [
                { userId: 1, userName: 'test1' },
                { userId: 2, userName: 'test2' },
            ],
        };

        const result = transformResponseData(data);
        expect(result).toEqual(expected);
    });

    test('should return data as is for null or undefined', () => {
        expect(transformResponseData(null)).toBeNull();
        expect(transformResponseData(undefined)).toBeUndefined();
    });

    test('should return data as is for non-string and non-object types', () => {
        expect(transformResponseData(123)).toBe(123);
        expect(transformResponseData(true)).toBe(true);
    });
});

describe('transformParams', () => {
    test('should return undefined when params is undefined', () => {
        const result = transformParams(undefined);
        expect(result).toBeUndefined();
    });

    test('should return null when params is null', () => {
        const result = transformParams(null);
        expect(result).toBeNull();
    });

    test('should return empty object when params is empty object', () => {
        const params = {};
        const result = transformParams(params);
        expect(result).toEqual({});
    });

    test('should convert camelCase keys to snake_case', () => {
        const params = {
            userId: 1,
            userName: 'test',
            isActive: true,
        };
        const expected = {
            user_id: 1,
            user_name: 'test',
            is_active: true,
        };
        const result = transformParams(params);
        expect(result).toEqual(expected);
    });

    test('should handle nested objects with deep transformation', () => {
        const params = {
            userId: 1,
            userDetails: {
                firstName: 'John',
                lastName: 'Doe',
                contactInfo: {
                    phoneNumber: '************',
                    emailAddress: '<EMAIL>',
                },
            },
        };
        const expected = {
            user_id: 1,
            user_details: {
                first_name: 'John',
                last_name: 'Doe',
                contact_info: {
                    phone_number: '************',
                    email_address: '<EMAIL>',
                },
            },
        };
        const result = transformParams(params);
        expect(result).toEqual(expected);
    });

    test('should handle arrays with nested objects', () => {
        const params = {
            userList: [
                { userId: 1, userName: 'user1' },
                { userId: 2, userName: 'user2' },
            ],
            metaData: {
                totalCount: 2,
                hasMore: false,
            },
        };
        const expected = {
            user_list: [
                { user_id: 1, user_name: 'user1' },
                { user_id: 2, user_name: 'user2' },
            ],
            meta_data: {
                total_count: 2,
                has_more: false,
            },
        };
        const result = transformParams(params);
        expect(result).toEqual(expected);
    });

    test('should handle mixed data types', () => {
        const params = {
            stringValue: 'test',
            numberValue: 42,
            booleanValue: true,
            nullValue: null,
            undefinedValue: undefined,
            arrayValue: [1, 2, 3],
            dateValue: new Date('2023-01-01'),
        };
        const expected = {
            string_value: 'test',
            number_value: 42,
            boolean_value: true,
            null_value: null,
            undefined_value: undefined,
            array_value: [1, 2, 3],
            date_value: new Date('2023-01-01'),
        };
        const result = transformParams(params);
        expect(result).toEqual(expected);
    });

    test('should not modify the original object', () => {
        const originalParams = {
            userId: 1,
            userName: 'test',
            userDetails: {
                firstName: 'John',
            },
        };
        const paramsCopy = JSON.parse(JSON.stringify(originalParams));

        transformParams(originalParams);

        expect(originalParams).toEqual(paramsCopy);
    });

    test('should handle keys that are already in snake_case', () => {
        const params = {
            user_id: 1,
            user_name: 'test',
            is_active: true,
        };
        const expected = {
            user_id: 1,
            user_name: 'test',
            is_active: true,
        };
        const result = transformParams(params);
        expect(result).toEqual(expected);
    });

    test('should handle complex nested structures', () => {
        const params = {
            requestData: {
                filterCriteria: {
                    dateRange: {
                        startDate: '2023-01-01',
                        endDate: '2023-12-31',
                    },
                    statusList: ['active', 'pending'],
                },
                sortOptions: {
                    sortBy: 'createdAt',
                    sortOrder: 'desc',
                },
            },
            pageInfo: {
                pageNumber: 1,
                pageSize: 10,
            },
        };
        const expected = {
            request_data: {
                filter_criteria: {
                    date_range: {
                        start_date: '2023-01-01',
                        end_date: '2023-12-31',
                    },
                    status_list: ['active', 'pending'],
                },
                sort_options: {
                    sort_by: 'createdAt',
                    sort_order: 'desc',
                },
            },
            page_info: {
                page_number: 1,
                page_size: 10,
            },
        };
        const result = transformParams(params);
        expect(result).toEqual(expected);
    });
});
