import { useRef } from 'react';
import { useTranslation } from 'react-i18next';

import type { ColProps, RowProps } from 'antd';
import { Col, Form, Input, Row } from 'antd';
import type { Rule } from 'antd/es/form';
import type { InputProps } from 'antd/lib';

import CustomRequiredMark from 'common/CustomRequiredMark';
import FormSelectSearchPagination from 'common/FormSelectSearchPagination';
import type { ITFFormSelectSearchPaginationProps } from 'common/FormSelectSearchPagination/FormSelectSearchPagination';

import { validateMaxByte } from 'helper/Validators';

export interface ITFFormSelectSearchPaginationSplitFieldsProps<Values = {}>
    extends Omit<ITFFormSelectSearchPaginationProps, 'name' | 'rules' | 'placholder'> {
    label?: string;
    required?: boolean;
    disabled?: boolean;
    hidden?: boolean;
    hiddenLabel?: boolean;
    customLabel?: React.ReactNode;

    // select
    nameSelect: keyof Values;
    placeholderSelect?: string;
    disabledSelect?: boolean;
    requiredSelect?: boolean;
    rulesSelect?: Rule[];
    labelText?: string;

    // input text
    nameInputText: keyof Values;
    placeholderInputText?: string;
    maxByte?: number;
    disabledInputText?: boolean;
    requiredInputText?: boolean;
    rulesInputText?: Rule[];
    labelInputText?: string;
    inputProps?: InputProps;
    rowProps?: RowProps;
    colProps?: {
        select?: ColProps;
        inputText?: ColProps;
    };
}

const FormSelectSearchPaginationSplitFields = <Values,>(props: ITFFormSelectSearchPaginationSplitFieldsProps<Values>) => {
    const { t } = useTranslation();

    const ref = useRef<HTMLInputElement>(null);

    const {
        // select
        nameSelect,
        placeholderSelect,
        disabledSelect = false,
        requiredSelect = false,
        rulesSelect = [],
        labelText,

        // input text
        nameInputText,
        placeholderInputText,
        maxByte,
        disabledInputText = false,
        requiredInputText = false,
        rulesInputText = [],
        labelInputText,

        // base
        id,
        label,
        disabled = false,
        required = false,
        formItemProps,
        selectInputProps,
        modalProps,
        responseDataKey = 'data',
        searchable = true,
        rowProps = {},
        colProps = {},

        hidden,
        customLabel,
        inputProps,
    } = props;

    const popupMatchSelectWidth = selectInputProps?.popupMatchSelectWidth ?? (ref.current ? ref.current.offsetWidth - 10 : 350);
    const { gutter } = rowProps;

    return (
        <>
            {customLabel ?? <CustomRequiredMark label={label} required={required || requiredSelect || requiredInputText} />}
            <Row {...rowProps} gutter={gutter ?? 10} ref={ref}>
                <Col {...colProps?.select} span={colProps?.select?.span ?? 8}>
                    <FormSelectSearchPagination
                        {...props}
                        id={id}
                        label={''}
                        customLabel={false}
                        name={nameSelect as string}
                        placeholder={placeholderSelect}
                        disabled={disabled || disabledSelect}
                        responseDataKey={responseDataKey}
                        searchable={searchable}
                        rules={[
                            ...rulesSelect,
                            {
                                required: required || requiredSelect,
                                message: `${labelText ?? label} ${t('is required')}`,
                            },
                        ]}
                        modalProps={{
                            ...modalProps,
                            title: modalProps?.title ?? label,
                        }}
                        formItemProps={{
                            ...formItemProps,
                            required: formItemProps?.required ?? false,
                            hidden: formItemProps?.hidden ?? hidden,
                        }}
                        selectInputProps={{
                            ...selectInputProps,
                            popupMatchSelectWidth,
                            optionLabelProp: selectInputProps?.optionLabelProp ?? 'value',
                        }}
                    />
                </Col>
                <Col {...colProps?.inputText} span={colProps?.inputText?.span ?? 16}>
                    <Form.Item
                        name={nameInputText as string}
                        rules={[
                            ...rulesInputText,
                            {
                                required: required || requiredInputText,
                                message: `${labelInputText ?? label} ${t('is required')}`,
                            },
                            () => ({
                                validator: (_, val) => {
                                    if (maxByte) {
                                        return validateMaxByte(val, maxByte, labelInputText ?? label ?? '');
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                        required={false}
                        hidden={hidden}
                    >
                        <Input {...inputProps} disabled={disabled || disabledInputText} placeholder={placeholderInputText}></Input>
                    </Form.Item>
                </Col>
            </Row>
        </>
    );
};

export default FormSelectSearchPaginationSplitFields;
