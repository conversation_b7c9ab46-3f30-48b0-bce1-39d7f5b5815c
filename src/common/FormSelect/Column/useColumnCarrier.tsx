import { useTranslation } from 'react-i18next';

import type { ColumnsType } from 'antd/lib/table';

import { useCommonColumnCarrier } from 'common/CommonColum/useCommonColumnCarrier';

import type { ITFCarrier } from 'types/Carrier.type';

const useColumnCarrier = (prefixID?: string) => {
    const { t } = useTranslation();
    const { configColumnCarrier: configColumn } = useCommonColumnCarrier({ prefixID });
    const columnsCarrier: ColumnsType<ITFCarrier> = [
        {
            dataIndex: 'select',
            width: 80,
            onHeaderCell: () => ({
                id: `${prefixID}LovCarrierSelectItem`,
            }),
        },
        {
            ...configColumn.carrierCode,
            title: t('Carrier Code'),
        },
        {
            ...configColumn.carrierName,
            title: t('Carrier Name'),
        },
    ];

    return {
        columnsCarrier,
    };
};

export default useColumnCarrier;
