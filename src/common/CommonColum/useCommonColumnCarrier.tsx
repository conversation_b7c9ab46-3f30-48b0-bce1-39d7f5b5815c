import { useTranslation } from 'react-i18next';

import type { ColumnType } from 'antd/es/table';

export interface ITFCommonColumnPlanLoad<T = {}> {
    carrierCode?: ColumnType<T>;
    carrierName?: ColumnType<T>;
}

interface ITFProps {
    prefixID?: string;
}

export const useCommonColumnCarrier = <T extends object>(props?: ITFProps) => {
    const { t } = useTranslation();
    const { prefixID = '' } = props ?? {};

    const configColumnCarrier: ITFCommonColumnPlanLoad<T> = {
        carrierCode: {
            title: t('Carrier Code'),
            dataIndex: 'carrierCode',
            width: 90,
            ellipsis: true,
            onHeaderCell: () => ({
                id: `${prefixID}LovCarrierCode`,
                align: 'center',
            }),
            onCell: (record: T, rowIndex: any) => ({
                id: `${prefixID}LovCarrierCode_${rowIndex}`,
                align: 'center',
            }),
            sorter: true,
        },
        carrierName: {
            title: t('Carrier Name'),
            dataIndex: 'carrierNameEng',
            width: 180,
            ellipsis: true,
            onHeaderCell: () => ({
                id: `${prefixID}LovCarrierName`,
                align: 'center',
            }),
            onCell: (record: T, rowIndex: any) => ({
                id: `${prefixID}LovCarrierName_${rowIndex}`,
                align: 'left',
            }),
            sorter: true,
        },
    };

    return { configColumnCarrier };
};
