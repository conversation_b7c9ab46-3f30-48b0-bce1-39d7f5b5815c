import type { ITFDelegatePermission, ITFTableDelegatePermission } from 'types/Setting/Permission/DelegatePermission.type';

export const transformDataDelegatePermission = (data: ITFDelegatePermission[], offset = 0): ITFTableDelegatePermission[] => {
    const res = data.map((item: ITFDelegatePermission, index: number): ITFTableDelegatePermission => {
        const listApprovers: string[] = [];
        item.approvers?.forEach((approver: any) => {
            listApprovers.push(`${JSON.parse(approver.approverInfo).first_name} ${JSON.parse(approver.approverInfo).last_name}`);
        });
        return {
            no: index + 1 + offset,
            key: `${item.id ?? index + 1}`,
            id: item.id,
            flowName: item.flowName,
            activityName: item.activityName,
            substituteGroupId: item.substituteGroupId,
            substituteName: item.substituteInfo
                ? `${JSON.parse(item.substituteInfo).first_name} ${JSON.parse(item.substituteInfo).last_name}` : '',
            substituteInfo: item.substituteInfo,
            approvers: listApprovers,
            startDate: item.startDate,
            endDate: item.endDate,
        };
    });
    return res;
};
