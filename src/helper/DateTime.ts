import type { TimePickerProps } from 'antd';

import { range } from './Array';

import type { Dayjs, OpUnitType, QUnitType } from 'dayjs';
import dayjs from 'dayjs';

type DisableTimeConfig = {
    startDate: Dayjs | null;
    endDate: Dayjs | null;
    referenceTime: Dayjs | null; // ใช้เทียบเวลา
    selectedTime: Dayjs | null; // เวลาใน time picker
    isStartTime: boolean; // ถ้า true = disable start time, false = disable end time
    dateFormatter: (date: Dayjs) => string;
};

export const getNow = () => {
    return dayjs();
};

export const getElapsedTime = (start?: Dayjs, end?: Dayjs, unit: QUnitType | OpUnitType = 'second', float: boolean = true) => {
    return end && start ? end.diff(start, unit, float) : null;
};

export const formatSecondsToMinuteSecond = (totalSeconds: number, format = 'm:ss') => {
    const formatted = dayjs.duration(totalSeconds, 'seconds').format(format);
    return formatted;
};

export const getDisabledTime = ({
    startDate,
    endDate,
    referenceTime,
    selectedTime,
    isStartTime,
    dateFormatter,
}: DisableTimeConfig): TimePickerProps['disabledTime'] => {
    return () => {
        const startStr = startDate ? dateFormatter(startDate) : '';
        const endStr = endDate ? dateFormatter(endDate) : '';
        const sameDay = startStr === endStr;

        if (!startDate || !endDate || !referenceTime || !sameDay) {
            return {
                disabledHours: () => [],
                disabledMinutes: () => [],
            };
        }

        const refHour = +dayjs(referenceTime).format('HH');
        const refMinute = +dayjs(referenceTime).format('mm');

        return {
            disabledHours: () =>
                isStartTime
                    ? range(0, 24).splice(refHour + 1, 24) // disable ชั่วโมงหลัง reference
                    : range(0, refHour), // disable ชั่วโมงก่อน reference

            disabledMinutes: () => {
                if (!selectedTime) return []; // ยังไม่เลือกเวลา => ไม่ disable นาที

                const selectedHour = +dayjs(selectedTime).format('HH');
                return selectedHour === refHour ? (isStartTime ? range(0, 60).splice(refMinute + 1, 60) : range(0, refMinute)) : [];
            },
        };
    };
};
