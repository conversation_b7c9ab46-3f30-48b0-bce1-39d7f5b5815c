import type { ITFPaginationParamsValue } from 'types/Pagination.type';
import type { ITFTable } from 'types/Table.type';
import type { ITFUser } from 'types/User.type';

import type { ITFMasterDataCompany } from '../MasterData/Company.type';
import type { ITFMasterDataCustomer } from '../MasterData/Customer.type';
import type { ITFMasterDataOrganization } from '../MasterData/Organization.type';

export interface ITFSaveDelegatePermission {
    flowName: string;
    activityName: string;
    approvers: string[];
    substituteName: string;
    startDate: string;
    endDate: string;
}

export interface ITFSubstitute {
    id: number;
    substituteName: string;
    substituteInfo: {
        user_id: string;
        first_name: string;
        last_name: string;
    };
}

export interface ITFDelegatePermission extends ITFSaveDelegatePermission {
    id: string;
    stage: string;
    memoCode?: string;
    createdAt?: string;
    updatedAt?: string;
    createdBy?: string;
    updatedBy?: string;

    company?: ITFMasterDataCompany | null;
    customer?: ITFMasterDataCustomer | null;
    delegatePermissionOrg?: ITFMasterDataOrganization[] | null;

    approverInfo?: {
        user_id: string;
        first_name: string;
        last_name: string;
    };

    substituteGroupId?: number;

    // ✅ เปลี่ยนให้เป็น list ของ approvers
    approverList?: ITFSubstitute[];
    approverUsers?: ITFUser[] | null;

    // ✅ เดี่ยว: substitute (ผู้แทนคนเดียว)
    substitute?: {
        username: string;
        userId: string | number;
        firstname: string;
        surname: string;
    };
    substituteInfo?:string;

    updatedName?: string;
    createdName?: string;
}

export interface ITFDataTableDelegatePermission extends ITFDelegatePermission {
    no: number;
    key: React.Key;
}

export interface ITFGetDelegatePermissionResponse {
    data: ITFDelegatePermission[];
    total: number;
    metadata: Metadata;
}

export interface Metadata {
    filter: Filter;
}

export interface Filter {
    startYear: string[];
    startMonth: string[];
    endYear: string[];
    endMonth: string[];
}

export interface ITFDelegatePermissionParams extends ITFPaginationParamsValue {
    flowName?: string | null;
    companyCode?: string | null;
    orgCode?: string | null;
    approverName?: string | null;
    substituteName?: string | null;
    search?: string;
    startMonth?: string | null;
    startYear?: string | null;
    endMonth?: string | null;
    endYear?: string | null;
    updatedAt?: string | null;
    updatedName?: string | null;
}

export interface ITFTableDelegatePermission extends ITFTable {
    id?: string | null;
    flowName?: string | null;
    activityName?: string | null;
    approvers?: string[] | null;
    substituteGroupId?: number | null;
    substituteName?: string | null;
    substituteInfo?: string | null;
    startDate?: string | null;
    endDate?: string | null;
    createdName?: string | null;
    createdAt?: string | null;
    updatedName?: string | null;
    updatedAt?: string | null;
}

export interface ITFGetDelegatePermissionParams extends ITFDelegatePermissionParams, ITFPaginationParamsValue {}

export interface ITFDelegatePermissionForm {
    flowName?: string | null;
    activityName?: string | null;
    searchApprover?: string | null;
    searchSubstitute?: string[] | null;
    startDate?: string | null;
    startTime?: string | null;
    endDate?: string | null;
    endTime?: string | null;
}

export interface ITFDelegatePermissionFormSearch {
    searchFlow?: string | null;
    searchCompany?: string | null;
    searchCustomer?: string | null;
    searchOrg?: string | null;
    searchApprover?: string | null;
    searchSubstitute?: string[] | null;
}

export interface ITFDelegatePermissionFormFilter {
    searchInput?: string;
    startMonth?: string[];
    startYear?: string[];
    endMonth?: string[];
    endYear?: string[];
}
