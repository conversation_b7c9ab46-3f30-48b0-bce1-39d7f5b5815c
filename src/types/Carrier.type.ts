import type { TypePagination } from './Pagination.type';
import type { ITFTable } from './Table.type';
import type { TypeDateAlias } from './TypeDate.type';
import type { TypeNumberAlias } from './TypeNumber.type';
import type { TypeStringAlias } from './TypeString.type';

export interface ITFCarrier {
    id?: TypeNumberAlias;
    carrierCode?: TypeStringAlias;
    carrierNameEng?: TypeStringAlias;
    carrierNameTha?: TypeStringAlias;
    taxType?: TypeStringAlias;
    taxId?: TypeStringAlias;
    taxRegType?: TypeStringAlias;
    taxRegDate?: TypeDateAlias;
    terms?: TypeNumberAlias;
    termType?: TypeStringAlias;
    vatId?: TypeStringAlias;
    address1Eng?: TypeStringAlias;
    address1Tha?: TypeStringAlias;
    address2Eng?: TypeStringAlias;
    address2Tha?: TypeStringAlias;
    stateCode?: TypeStringAlias;
    cityCode?: TypeStringAlias;
    postCode?: TypeStringAlias;
    telNo?: TypeStringAlias;
    docStatus?: TypeStringAlias;
    faxNo?: TypeStringAlias;
    email?: TypeStringAlias;
    nasegGroup?: TypeStringAlias;
    userOwner?: TypeStringAlias;
    createDate?: TypeDateAlias;
    textRemark?: TypeStringAlias;
    userUpdate?: TypeStringAlias;
    lastUpdateDate?: TypeDateAlias;
    glCvcode?: TypeStringAlias;
    refVendorCode?: TypeStringAlias;
    minimumWeight?: TypeNumberAlias;
    inttraFlag?: TypeStringAlias;
    scacCode?: TypeStringAlias;
    scacName?: TypeStringAlias;
    createdAt?: TypeDateAlias;
    updatedAt?: TypeDateAlias;
    createdBy?: TypeStringAlias;
    updatedBy?: TypeStringAlias;
}

export interface ITFCarrierParams {
    carrierCode?: string | null;
}

export interface ITFGetCarrierParams extends ITFCarrierParams, TypePagination {}

export interface ITFGetCarrierResponse {
    data: ITFCarrier[];
    total: number;
}

export interface ITFOptionCarrier extends ITFCarrier {
    value: string;
    label: string;
    data?: ITFCarrier;
}

export interface ITFTableCarrier extends ITFCarrier, ITFTable {}
