import type { ITFCarrier } from 'types/Carrier.type';
import type { TypeDateAlias } from 'types/TypeDate.type';
import type { TypeNumberAlias } from 'types/TypeNumber.type';
import type { TypeStringAlias } from 'types/TypeString.type';

export interface ITFAirFreightBookingInfo {
    id?: TypeNumberAlias;
    freightBookingInfoId?: TypeNumberAlias;
    requestNo?: TypeStringAlias;
    initId?: TypeStringAlias;
    initName?: TypeStringAlias;
    shipmentNo?: TypeStringAlias;
    invoiceNo?: TypeStringAlias;
    jobNo?: TypeStringAlias;
    jobDate?: TypeDateAlias;
    confirmDate?: TypeDateAlias;
    companyCode?: TypeStringAlias;
    companyNameEng?: TypeStringAlias;
    companyNameTha?: TypeStringAlias;
    consigneeCode?: TypeStringAlias;
    consigneeName?: TypeStringAlias;
    currentStatus?: TypeStringAlias;
    coLoadInvoice?: TypeStringAlias;
    coLoadType?: TypeStringAlias;
    orgCode?: TypeStringAlias;
    orgNameEng?: TypeStringAlias;
    orgNameTha?: TypeStringAlias;
    processType?: TypeStringAlias;
    exportType?: TypeStringAlias;
    bookingStatus?: TypeStringAlias;
    requestDate?: TypeDateAlias;
    ownerId?: TypeStringAlias;
    ownerName?: TypeStringAlias;
    assignToId?: TypeStringAlias;
    assignToName?: TypeStringAlias;
    assignFromId?: TypeStringAlias;
    assignFromName?: TypeStringAlias;
    assignteamId?: TypeStringAlias;
    assignteamName?: TypeStringAlias;
    planLoadInfoId?: TypeNumberAlias;
    transactionNo?: TypeNumberAlias;
    remarkFromCreator?: TypeStringAlias;
    commodityCode?: TypeStringAlias;
    commodityDesc?: TypeStringAlias;
    agentCarrierCode?: TypeStringAlias;
    agentCarrierName?: TypeStringAlias;
    agentCarrierData?: ITFCarrier | null;
    etdDatetime?: TypeDateAlias;
    etaDatetime?: TypeDateAlias;
    etaTime?: TypeStringAlias;
    etdTime?: TypeStringAlias;
    dueDate?: TypeDateAlias;
    stuffingDate?: TypeStringAlias;
    awbNo?: TypeStringAlias;
    wawbNo?: TypeStringAlias;
    hawbNo?: TypeStringAlias;
    bookingRef?: TypeStringAlias;
    awbDate?: TypeDateAlias;
    keyDate?: TypeDateAlias;
    connectingAt?: TypeStringAlias;
    chargeWeightQuantity?: TypeNumberAlias;
    chargeWeightUnit?: TypeStringAlias;
    dryIceQuantity?: TypeNumberAlias;
    dryIceUnit?: TypeStringAlias;
    taxTypeCode?: TypeStringAlias;
    taxTypeName?: TypeStringAlias;
    airLine?: TypeStringAlias;
    flightNo?: TypeStringAlias;
    secondAirLine?: TypeStringAlias;
    secondFlightNo?: TypeStringAlias;
    printBookingDate?: TypeDateAlias;
    confirmBookingDate?: TypeDateAlias;
}
